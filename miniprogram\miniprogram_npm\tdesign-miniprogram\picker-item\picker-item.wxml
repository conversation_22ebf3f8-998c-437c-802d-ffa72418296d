<wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="{{_.cls(classPrefix + '__group', [])}} class {{prefix}}-class" bind:touchstart="onTouchStart" catch:touchmove="onTouchMove" bind:touchend="onTouchEnd" bind:touchcancel="onTouchEnd"><view class="{{classPrefix}}__wrapper" style="transition: transform {{ duration }}ms cubic-bezier(0.215, 0.61, 0.355, 1); transform: translate3d(0, {{ offset }}px, 0)"><view class="{{_.cls(classPrefix + '__item', [['active', curIndex == index]])}}" style="height: {{pickItemHeight}}px" wx:for="{{formatOptions}}" wx:key="index" wx:for-item="option" data-index="{{ index }}" bind:tap="onClickItem"><text class="{{classPrefix}}__item-label">{{option[pickerKeys.label]}}</text><slot name="label-suffix--{{index}}"></slot></view></view></view>