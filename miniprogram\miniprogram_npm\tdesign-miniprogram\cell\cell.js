import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{calcIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-cell`;let Cell=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-title`,`${prefix}-class-description`,`${prefix}-class-note`,`${prefix}-class-hover`,`${prefix}-class-image`,`${prefix}-class-left`,`${prefix}-class-left-icon`,`${prefix}-class-center`,`${prefix}-class-right`,`${prefix}-class-right-icon`],this.relations={"../cell-group/cell-group":{type:"parent"}},this.options={multipleSlots:!0},this.properties=props,this.data={prefix:prefix,classPrefix:name,isLastChild:!1},this.observers={leftIcon(e){this.setIcon("_leftIcon",e,"")},rightIcon(e){this.setIcon("_rightIcon",e,"")},arrow(e){this.setIcon("_arrow",e,"chevron-right")}}}setIcon(e,t,s){this.setData({[e]:calcIcon(t,s)})}onClick(e){this.triggerEvent("click",e.detail),this.jumpLink()}jumpLink(e="url",t="jumpType"){const s=this.data[e],i=this.data[t];s&&wx[i]({url:s})}};Cell=__decorate([wxComponent()],Cell);export default Cell;