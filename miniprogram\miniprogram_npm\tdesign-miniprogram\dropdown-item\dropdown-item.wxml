<wxs module="_this" src="./index.wxs"/><wxs module="_" src="../common/utils.wxs"/><view wx:if="{{wrapperVisible}}" class="{{classPrefix}} class {{prefix}}-class" style="{{_._style([_this.getStyles(top, zIndex), style, customStyle])}}"><view class="{{ classPrefix }}__mask" hidden="{{!show}}" style="{{_._style(['height:' + maskHeight + 'px', style, customStyle])}}" bind:tap="handleMaskClick" catchtouchmove="closeDropdown"></view><t-popup visible="{{show}}" z-index="{{zIndex + 1}}" duration="{{duration}}" show-overlay="{{showOverlay}}" custom-style="position: absolute" overlay-props="{{ { style: 'position: absolute' } }}" bind:leaved="onLeaved" bind:visible-change="handleMaskClick" t-class="{{classPrefix}}__popup-host" t-class-content="{{classPrefix}}__content {{prefix}}-class-content"><view class="{{classPrefix}}__body"><scroll-view wx:if="{{!multiple && options && options.length > 0}}" class="{{classPrefix}}__scroll" scroll-y scroll-into-view="id_{{value}}"><t-radio-group class="{{classPrefix}}__radio {{prefix}}-class-column" t-class="{{classPrefix}}__radio-group" style="grid-template-columns: repeat({{optionsColumns}}, 1fr)" value="{{value}}" bind:change="handleRadioChange"><view wx:for="{{options}}" wx:key="index" id="id_{{item[valueAlias]}}"><t-radio placement="{{placement}}" tabindex="0" icon="line" class="{{classPrefix}}__radio-item {{prefix}}-class-column-item" t-class="radio" t-class-label="{{prefix}}-class-column-item-label" value="{{item[valueAlias]}}" label="{{item[labelAlias]}}" disabled="{{item.disabled}}"/></view></t-radio-group></scroll-view><scroll-view wx:if="{{multiple && options && options.length > 0}}" class="{{classPrefix}}__scroll" scroll-y scroll-into-view="id_{{firstCheckedValue}}"><t-checkbox-group class="{{classPrefix}}__checkbox {{prefix}}-class-column" t-class="{{classPrefix}}__checkbox-group" style="grid-template-columns: repeat({{optionsColumns}}, 1fr)" value="{{value ? value : []}}" bind:change="handleRadioChange"><view wx:for="{{options}}" wx:key="index" id="id_{{item[valueAlias]}}"><t-checkbox tabindex="0" class="{{classPrefix}}__checkbox-item {{prefix}}-class-column-item" theme="tag" value="{{item[valueAlias]}}" label="{{item[labelAlias]}}" disabled="{{item.disabled}}"></t-checkbox></view></t-checkbox-group></scroll-view><slot/></view><view class="{{classPrefix}}__footer {{prefix}}-class-footer"><slot name="footer"/><block wx:if="{{multiple}}"><t-button block class="{{classPrefix}}__footer-btn {{classPrefix}}__reset-btn" theme="light" content="重置" disabled="{{value.length == 0}}" bindtap="handleReset"/><t-button block class="{{classPrefix}}__footer-btn {{classPrefix}}__confirm-btn" theme="primary" content="确定" disabled="{{value.length == 0}}" bindtap="handleConfirm"/></block></view></t-popup></view>