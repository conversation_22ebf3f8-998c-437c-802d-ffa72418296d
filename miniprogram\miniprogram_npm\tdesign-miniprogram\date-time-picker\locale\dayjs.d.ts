import enLocale from 'dayjs/locale/en';
declare const _default: {
    default: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    en: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    'zh-cn': {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    zh: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    'zh-tw': {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    tc: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    ko: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    kr: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    ja: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
    ru: {
        key: string;
        label: string;
        locale: enLocale.Locale;
        i18n: {
            year: string;
            month: string;
            date: string;
            hour: string;
            minute: string;
            second: string;
            am: string;
            pm: string;
            confirm: string;
            cancel: string;
        };
    };
};
export default _default;
