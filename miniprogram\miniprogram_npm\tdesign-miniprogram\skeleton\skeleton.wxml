<wxs src="../common/utils.wxs" module="_"/><view wx:if="{{ isShow }}" style="{{_._style([style, customStyle])}}" class="{{classPrefix}} class {{prefix}}-class"><view wx:if="{{ parsedRowcols.length }}" class="{{classPrefix}}__content"><view wx:for="{{parsedRowcols}}" wx:for-index="index" wx:for-item="row" wx:key="index" class="{{classPrefix}}__row {{prefix}}-class-row"><view wx:for="{{ row }}" wx:for-index="index" wx:for-item="col" wx:key="index" class="{{col.class}} {{prefix}}-class-col" style="{{_._style(col.style)}}"></view></view></view></view><view wx:else class="class {{classPrefix}}__content"><slot/></view>