<wxs src="../common/utils.wxs" module="_"/><import src="../common/template/icon.wxml"/><view class="{{className}} class {{prefix}}-class" style="{{_._style([tagStyle, style, customStyle])}}" bind:tap="handleClick"><view aria-hidden="{{true}}" class="{{classPrefix}}__icon"><template wx:if="{{_icon}}" is="icon" data="{{ tClass: prefix + '-icon', ..._icon }}"/><slot name="icon"/></view><view class="{{classPrefix}}__text"><slot/></view><template wx:if="{{_closable}}" is="icon" data="{{tClass: classPrefix + '__icon-close ' + prefix + '-icon', bindclick: 'handleClose',  ariaRole: 'button', ariaLabel: '关闭',  ..._closable }}" catch:tap="handleClose"/><slot wx:else name="closable"/></view>