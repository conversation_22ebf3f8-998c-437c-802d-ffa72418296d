import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import useCustomNavbar from"../mixins/using-custom-navbar";const{prefix:prefix}=config,name=`${prefix}-drawer`;let Drawer=class extends SuperComponent{constructor(){super(...arguments),this.behaviors=[useCustomNavbar],this.externalClasses=[],this.options={multipleSlots:!0},this.properties=props,this.data={classPrefix:name},this.methods={visibleChange({detail:e}){const{visible:t}=e,{showOverlay:r}=this.data;this.setData({visible:t}),t||this.triggerEvent("close",{trigger:"overlay"}),r&&this.triggerEvent("overlay-click",{visible:t})},itemClick(e){const{index:t,item:r}=e.currentTarget.dataset;this.triggerEvent("item-click",{index:t,item:r})}}}};Drawer=__decorate([wxComponent()],Drawer);export default Drawer;