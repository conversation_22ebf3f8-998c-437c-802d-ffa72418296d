import{__decorate}from"tslib";import{wxComponent,SuperComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-switch`;let Switch=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=["t-class","t-class-label","t-class-body","t-class-dot"],this.behaviors=["wx://form-field"],this.properties=props,this.data={prefix:prefix,classPrefix:name,checked:!1},this.controlledProps=[{key:"value",event:"change"}],this.observers={value(e){const[t]=this.data.customValue;this.setData({checked:e===t})}},this.methods={handleSwitch(){const{loading:e,disabled:t,value:s,customValue:o}=this.data,[i,r]=o;e||t||this._trigger("change",{value:s===i?r:i})}}}};Switch=__decorate([wxComponent()],Switch);export default Switch;