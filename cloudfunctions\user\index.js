// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-2g263bi11fd6ea28'
})

const db = cloud.database()
const userCollection = db.collection('users')

/**
 * 用户登录
 * 通过 OPENID 查找用户，不存在则创建新用户
 */
async function login() {
  const wxContext = cloud.getWXContext()
  const { OPENID, APPID } = wxContext
  
  if (!OPENID) {
    return {
      code: -1,
      message: '获取用户OPENID失败'
    }
  }
  
  try {
    // 查询用户是否已存在
    const user = await userCollection.where({
      _openid: OPENID
    }).get()
    
    const now = db.serverDate()
    
    // 用户已存在，更新登录时间
    if (user.data && user.data.length > 0) {
      await userCollection.where({
        _openid: OPENID
      }).update({
        data: {
          lastLoginTime: now
        }
      })
      
      return {
        code: 0,
        message: '登录成功',
        data: {
          ...user.data[0],
          lastLoginTime: now
        }
      }
    } 
    // 用户不存在，创建新用户
    else {
      // 获取默认用户信息
      const newUser = {
        _openid: OPENID,
        appid: APPID,
        nickName: '微信用户',
        avatarUrl: '',
        createTime: now,
        lastLoginTime: now
      }
      
      const result = await userCollection.add({
        data: newUser
      })
      
      return {
        code: 0,
        message: '注册成功',
        data: {
          ...newUser,
          _id: result._id
        }
      }
    }
  } catch (error) {
    console.error('登录失败', error)
    return {
      code: -1,
      message: '登录失败: ' + error.message,
      error
    }
  }
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 */
async function updateUserInfo(userInfo) {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  
  if (!OPENID) {
    return {
      code: -1,
      message: '获取用户OPENID失败'
    }
  }
  
  try {
    // 更新用户信息
    await userCollection.where({
      _openid: OPENID
    }).update({
      data: {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        updateTime: db.serverDate()
      }
    })
    
    return {
      code: 0,
      message: '更新成功'
    }
  } catch (error) {
    console.error('更新用户信息失败', error)
    return {
      code: -1,
      message: '更新失败: ' + error.message,
      error
    }
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  
  if (!OPENID) {
    return {
      code: -1,
      message: '获取用户OPENID失败'
    }
  }
  
  try {
    const user = await userCollection.where({
      _openid: OPENID
    }).get()
    
    if (user.data && user.data.length > 0) {
      return {
        code: 0,
        message: '获取成功',
        data: user.data[0]
      }
    } else {
      return {
        code: -1,
        message: '用户不存在'
      }
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
    return {
      code: -1,
      message: '获取失败: ' + error.message,
      error
    }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  
  switch (action) {
    case 'login':
      return await login()
    case 'updateUserInfo':
      return await updateUserInfo(data)
    case 'getUserInfo':
      return await getUserInfo()
    default:
      return {
        code: -1,
        message: '未知操作类型'
      }
  }
} 