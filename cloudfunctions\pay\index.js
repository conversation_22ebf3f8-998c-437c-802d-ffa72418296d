// 云函数入口文件
const cloud = require('wx-server-sdk')
const crypto = require('crypto')
const xml2js = require('xml2js')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 自动获取当前环境，更灵活
})

// 微信支付相关配置
const config = {
  appid: 'wx4eb4759c07f270ae', // 小程序appid
  mchid: '1719907060', // 商户号，需要在微信支付商户平台获取
  apiKey: 'q1a1z1w1s1x1e1d1c1r1f1v1t1g1b1y1', // 微信支付API密钥，需要在微信支付商户平台设置
  notifyUrl: '', // 支付结果通知回调地址，需要公网可访问的URL
}

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
function generateNonceStr(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成支付签名
 * @param {Object} params 参数对象
 * @returns {string} 签名
 */
function generateSign(params) {
  // 1. 对所有参数按照字段名的ASCII码从小到大排序
  const sortedKeys = Object.keys(params).sort()
  
  // 2. 拼接成key=value&key=value形式
  let signStr = ''
  sortedKeys.forEach(key => {
    if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
      signStr += `${key}=${params[key]}&`
    }
  })
  
  // 3. 在末尾拼接key=API密钥
  signStr += `key=${config.apiKey}`
  
  // 4. MD5加密并转为大写
  return crypto.createHash('md5').update(signStr).digest('hex').toUpperCase()
}

/**
 * 生成订单号
 * @returns {string} 订单号
 */
function generateOrderId() {
  const now = new Date()
  const year = now.getFullYear().toString()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  
  return `${year}${month}${day}${hour}${minute}${second}${random}`
}

/**
 * 对象转XML
 * @param {Object} obj 对象
 * @returns {string} XML字符串
 */
function objectToXml(obj) {
  let xml = '<xml>'
  for (let key in obj) {
    xml += `<${key}>${obj[key]}</${key}>`
  }
  xml += '</xml>'
  return xml
}

/**
 * XML转对象
 * @param {string} xml XML字符串
 * @returns {Promise<Object>} 对象
 */
function xmlToObject(xml) {
  return new Promise((resolve, reject) => {
    const parser = new xml2js.Parser({ 
      explicitArray: false,
      trim: true 
    })
    
    parser.parseString(xml, (err, result) => {
      if (err) {
        reject(err)
      } else {
        // 简化结果，去除xml标记
        resolve(result.xml)
      }
    })
  })
}

/**
 * 发送HTTP请求
 * @param {string} url 请求地址 
 * @param {string} data 请求数据
 * @returns {Promise<Object>} 响应结果
 */
async function sendRequest(url, data) {
  try {
    const result = await cloud.callFunction({
      name: 'httpRequest',
      data: {
        url: url,
        method: 'POST',
        body: data,
        headers: {
          'Content-Type': 'text/xml'
        }
      }
    })
    
    return result.result.body
  } catch (error) {
    console.error('HTTP请求失败', error)
    throw new Error('请求微信支付API失败')
  }
}

/**
 * 计算会员过期时间
 * @param {string} planType 会员类型
 * @returns {Date} 过期时间
 */
function calculateExpireTime(planType) {
  const now = new Date()
  
  switch (planType) {
    case 'monthly':
      // 月度会员 - 30天
      return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    case 'quarterly':
      // 季度会员 - 90天
      return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000)
    case 'yearly':
      // 年度会员 - 365天
      return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000)
    default:
      // 默认30天
      return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  }
}

/**
 * 统一下单API
 * @param {Object} orderInfo 订单信息
 * @returns {Promise<Object>} 预支付信息
 */
async function unifiedOrder(orderInfo) {
  try {
    const { agentId, agentTitle, planType, amount, openid } = orderInfo
    
    if (!openid) {
      throw new Error('缺少openid参数')
    }
    
    if (!config.mchid || !config.apiKey) {
      throw new Error('请先配置商户号和API密钥')
    }
    
    // 计算会员过期时间
    const expireTime = calculateExpireTime(planType)
    
    // 订单金额，单位为分
    const totalFee = Math.floor(amount * 100)
    
    // 生成商户订单号
    const outTradeNo = generateOrderId()
    
    // 构建统一下单请求参数
    const params = {
      appid: config.appid,
      mch_id: config.mchid,
      nonce_str: generateNonceStr(),
      body: `智能体广场-${agentTitle}-${planType === 'monthly' ? '月度' : planType === 'quarterly' ? '季度' : '年度'}会员`,
      out_trade_no: outTradeNo,
      total_fee: totalFee,
      spbill_create_ip: '127.0.0.1', // 调用微信支付API的机器IP
      notify_url: config.notifyUrl || 'https://example.com/notify', // 默认值，实际开发中需要替换
      trade_type: 'JSAPI',
      openid: openid
    }
    
    // 生成签名
    params.sign = generateSign(params)
    
    // 构建XML请求体
    const xmlBody = objectToXml(params)
    
    // 调用统一下单API
    const xmlResponse = await sendRequest('https://api.mch.weixin.qq.com/pay/unifiedorder', xmlBody)
    
    // 解析XML响应
    const result = await xmlToObject(xmlResponse)
    
    if (result.return_code !== 'SUCCESS' || result.result_code !== 'SUCCESS') {
      console.error('统一下单失败', result)
      throw new Error(result.return_msg || result.err_code_des || '统一下单失败')
    }
    
    // 生成小程序调用支付所需的参数
    const payParams = {
      appId: config.appid,
      timeStamp: String(Math.floor(Date.now() / 1000)),
      nonceStr: generateNonceStr(),
      package: `prepay_id=${result.prepay_id}`,
      signType: 'MD5'
    }
    
    // 生成支付签名
    payParams.paySign = generateSign(payParams)
    
    // 保存订单信息到云数据库
    const db = cloud.database()
    await db.collection('orders').add({
      data: {
        outTradeNo,
        agentId,
        agentTitle,
        planType,
        amount,
        totalFee,
        openid: openid,
        status: 'NOTPAY', // 未支付
        expireTime, // 保存过期时间
        createTime: db.serverDate()
      }
    })
    
    return {
      code: 0,
      message: '下单成功',
      data: {
        ...payParams,
        outTradeNo
      }
    }
  } catch (error) {
    console.error('统一下单失败', error)
    return {
      code: -1,
      message: '下单失败: ' + error.message,
      error
    }
  }
}

/**
 * 查询订单API
 * @param {string} outTradeNo 商户订单号
 * @returns {Promise<Object>} 订单信息
 */
async function queryOrder(outTradeNo) {
  try {
    if (!config.mchid || !config.apiKey) {
      throw new Error('请先配置商户号和API密钥')
    }
    
    // 构建查询订单请求参数
    const params = {
      appid: config.appid,
      mch_id: config.mchid,
      out_trade_no: outTradeNo,
      nonce_str: generateNonceStr()
    }
    
    // 生成签名
    params.sign = generateSign(params)
    
    // 构建XML请求体
    const xmlBody = objectToXml(params)
    
    // 调用查询订单API
    const xmlResponse = await sendRequest('https://api.mch.weixin.qq.com/pay/orderquery', xmlBody)
    
    // 解析XML响应
    const result = await xmlToObject(xmlResponse)
    
    if (result.return_code !== 'SUCCESS') {
      console.error('查询订单失败', result)
      throw new Error(result.return_msg || '查询订单失败')
    }
    
    // 提取交易状态
    const tradeState = result.trade_state
    const db = cloud.database()
    
    // 获取订单信息
    const orderInfo = await db.collection('orders').where({
      outTradeNo
    }).get()
    
    if (!orderInfo.data || orderInfo.data.length === 0) {
      throw new Error('订单不存在')
    }
    
    const order = orderInfo.data[0]
    
    // 更新订单状态
    await db.collection('orders').where({
      outTradeNo
    }).update({
      data: {
        status: tradeState,
        updateTime: db.serverDate()
      }
    })
    
    // 如果支付成功，更新会员信息
    if (tradeState === 'SUCCESS') {
      // 调用更新会员信息方法
      await updateMemberInfo({
        openid: order.openid,
        agentId: order.agentId,
        agentTitle: order.agentTitle,
        planType: order.planType,
        outTradeNo: order.outTradeNo,
        expireTime: order.expireTime
      })
    }
    
    return {
      code: 0,
      message: '查询成功',
      data: {
        tradeState,
        orderInfo: order
      }
    }
  } catch (error) {
    console.error('查询订单失败', error)
    return {
      code: -1,
      message: '查询订单失败: ' + error.message,
      error
    }
  }
}

/**
 * 更新用户会员信息
 * @param {Object} memberInfo 会员信息
 * @returns {Promise<Object>} 操作结果
 */
async function updateMemberInfo(memberInfo) {
  try {
    const { openid, agentId, agentTitle, planType, outTradeNo, expireTime } = memberInfo
    const db = cloud.database()
    
    // 查询用户是否已有会员记录
    const userRecord = await db.collection('members').where({
      openid,
      agentId
    }).get()
    
    const now = db.serverDate()
    
    if (userRecord.data && userRecord.data.length > 0) {
      // 已有记录，更新会员信息
      await db.collection('members').doc(userRecord.data[0]._id).update({
        data: {
          planType,
          expireTime,
          updatedAt: now,
          outTradeNo,
          status: 'active'
        }
      })
    } else {
      // 没有记录，创建新会员记录
      await db.collection('members').add({
        data: {
          openid,
          agentId,
          agentTitle,
          planType,
          expireTime,
          createdAt: now,
          updatedAt: now,
          outTradeNo,
          status: 'active'
        }
      })
    }
    
    return {
      code: 0,
      message: '会员信息更新成功'
    }
  } catch (error) {
    console.error('更新会员信息失败', error)
    return {
      code: -1,
      message: '更新会员信息失败: ' + error.message,
      error
    }
  }
}

/**
 * 获取用户会员信息
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 会员信息
 */
async function getUserMemberships(params) {
  try {
    const { openid, agentId } = params
    
    if (!openid) {
      throw new Error('缺少openid参数')
    }
    
    const db = cloud.database()
    let query = db.collection('members').where({
      openid
    })
    
    // 如果提供了特定智能体ID，则精确查询
    if (agentId) {
      query = query.where({
        agentId
      })
    }
    
    // 查询用户的会员信息
    const result = await query.get()
    
    // 获取当前时间
    const now = new Date()
    
    // 处理会员状态
    const memberships = result.data.map(item => {
      // 检查会员是否过期
      const expireTime = new Date(item.expireTime)
      const isExpired = now > expireTime
      
      return {
        ...item,
        isExpired,
        // 更新状态：如果已过期，则状态为expired
        status: isExpired ? 'expired' : item.status
      }
    })
    
    // 如果有过期的会员，更新状态
    for (const membership of memberships) {
      if (membership.isExpired && membership.status === 'active') {
        await db.collection('members').doc(membership._id).update({
          data: {
            status: 'expired'
          }
        })
      }
    }
    
    return {
      code: 0,
      message: '获取会员信息成功',
      data: memberships
    }
  } catch (error) {
    console.error('获取会员信息失败', error)
    return {
      code: -1,
      message: '获取会员信息失败: ' + error.message,
      error
    }
  }
}

/**
 * 获取用户订单记录
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 订单记录
 */
async function getUserOrders(params) {
  try {
    const { openid, agentId, page = 1, pageSize = 10 } = params
    
    if (!openid) {
      throw new Error('缺少openid参数')
    }
    
    const db = cloud.database()
    const _ = db.command
    let query = db.collection('orders').where({
      openid
    })
    
    // 如果提供了特定智能体ID，则精确查询
    if (agentId) {
      query = query.where({
        agentId
      })
    }
    
    // 获取总记录数
    const countResult = await query.count()
    const total = countResult.total
    
    // 分页查询订单
    const orders = await query
      .orderBy('createTime', 'desc') // 按创建时间倒序
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()
    
    // 处理订单记录，增加可读性更好的状态说明
    const orderRecords = orders.data.map(order => {
      return {
        ...order,
        statusText: getOrderStatusText(order.status),
        createTimeFormatted: formatDate(order.createTime),
        amountFormatted: (order.amount || 0).toFixed(2)
      }
    })
    
    return {
      code: 0,
      message: '获取订单记录成功',
      data: {
        orders: orderRecords,
        pagination: {
          total,
          page,
          pageSize,
          pages: Math.ceil(total / pageSize)
        }
      }
    }
  } catch (error) {
    console.error('获取订单记录失败', error)
    return {
      code: -1,
      message: '获取订单记录失败: ' + error.message,
      error
    }
  }
}

/**
 * 获取订单状态的文本说明
 * @param {string} status 订单状态
 * @returns {string} 状态文本
 */
function getOrderStatusText(status) {
  switch (status) {
    case 'SUCCESS':
      return '支付成功'
    case 'REFUND':
      return '已退款'
    case 'NOTPAY':
      return '未支付'
    case 'CLOSED':
      return '已关闭'
    case 'REVOKED':
      return '已撤销'
    case 'USERPAYING':
      return '用户支付中'
    case 'PAYERROR':
      return '支付失败'
    default:
      return '未知状态'
  }
}

/**
 * 格式化日期为 YYYY-MM-DD HH:MM:SS 格式
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  if (!date) return ''
  
  const d = new Date(date)
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('云函数调用', {action, data, openid})
  
  switch (action) {
    case 'unifiedOrder':
      // 统一下单
      return await unifiedOrder({
        ...data,
        openid
      })
    case 'queryOrder':
      // 查询订单
      return await queryOrder(data.outTradeNo)
    case 'updateMemberInfo':
      // 更新会员信息
      return await updateMemberInfo(data)
    case 'getUserMemberships':
      // 获取用户会员信息
      return await getUserMemberships(data)
    case 'getUserOrders':
      // 获取用户订单记录
      return await getUserOrders(data)
    default:
      return {
        code: -1,
        message: '未知操作类型'
      }
  }
} 