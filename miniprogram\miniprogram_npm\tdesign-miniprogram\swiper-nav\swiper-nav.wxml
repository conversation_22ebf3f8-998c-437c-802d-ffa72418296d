<wxs src="../common/utils.wxs" module="_"/><view wx:if="{{showControls}}" class="class {{prefix}}-class {{classPrefix}}__btn" style="{{_._style([style, customStyle])}}"><view class="{{classPrefix}}__btn--prev" bind:tap="nav" data-dir="prev" aria-role="button" aria-label="上一张"/><view class="{{classPrefix}}__btn--next" bind:tap="nav" data-dir="next" aria-role="button" aria-label="下一张"/></view><view wx:if="{{total >= minShowNum}}" style="{{_._style([style, customStyle])}}" class="class {{prefix}}-class {{classPrefix}} {{classPrefix}}--{{direction}} {{classPrefix}}__{{type}} {{classPrefix}}--{{paginationPosition}}"><block wx:if="{{ type === 'dots' || type === 'dots-bar'}}"><view wx:for="{{total}}" wx:for-index="idx" wx:key="idx" class="{{_.cls(classPrefix + '__' + type + '-item', [['active', current === idx], direction])}}"/></block><block wx:if="{{ type === 'fraction'}}">{{current + 1}}/{{total}}</block></view>