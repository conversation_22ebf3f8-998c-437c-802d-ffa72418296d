<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><view wx:if="{{visible}}" style="{{_._style([style, customStyle])}}" class="{{classPrefix}} {{classPrefix}}--{{theme}} class {{prefix}}-class"><view class="{{classPrefix}}__prefix-icon" bind:tap="clickPrefixIcon"><slot name="prefix-icon"/><template wx:if="{{_prefixIcon}}" is="icon" data="{{tClass: prefix + '-class-prefix-icon', ..._prefixIcon }}"></template></view><view class="{{classPrefix}}__content-wrap" bind:tap="clickContent"><view wx:if="{{ direction === 'vertical' && _.isArray(content) }}"><swiper autoplay="true" vertical="true" circular="true" interval="{{interval}}" display-multiple-items="1" class="{{classPrefix}}__content {{classPrefix}}__content--vertical" bindchange="onChange"><block wx:for="{{content}}" wx:key="index"><swiper-item><view class="{{classPrefix}}__content--vertical-item">{{item}}</view></swiper-item></block></swiper></view><view wx:else class="{{classPrefix}}__content {{prefix}}-class-content {{ !marquee ? classPrefix + '__content-wrapable' : ''}}" animation="{{ animationData }}"><block wx:if="{{content}}">{{content}}</block><slot name="content"/><view class="{{classPrefix}}__operation {{prefix}}-class-operation" catch:tap="clickOperation"><block wx:if="{{ operation }}">{{operation}}</block><slot name="operation"/></view></view></view><view class="{{classPrefix}}__suffix-icon" bind:tap="clickSuffixIcon"><slot name="suffix-icon"/><template wx:if="{{_suffixIcon}}" is="icon" data="{{tClass: prefix + '-class-suffix-icon', ..._suffixIcon }}"></template></view></view>