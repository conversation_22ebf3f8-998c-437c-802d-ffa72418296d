<import src="../common/template/badge.wxml"/><import src="../common/template/icon.wxml"/><wxs src="./tabs.wxs" module="_tabs"/><wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="{{_.cls(classPrefix, [placement])}} class {{prefix}}-class"><t-sticky t-class="{{_.cls(classPrefix + '__sticky', [placement])}}" disabled="{{ !sticky }}" z-index="{{ stickyProps.zIndex || '1' }}" offset-top="{{ stickyProps.offsetTop || 0 }}" container="{{ stickyProps.container }}" bind:scroll="onTouchScroll"><view class="{{_.cls(classPrefix + '__wrapper', [theme])}}"><scroll-view class="{{_.cls(classPrefix + '__scroll', [placement, ['split', split]])}}" enhanced enable-flex scroll-left="{{offset}}" scroll-x="{{true}}" scroll-anchoring scroll-with-animation enable-passive show-scrollbar="{{false}}" type="list" bind:scroll="onScroll"><view class="{{_.cls(classPrefix + '__nav', [placement, ['evenly', spaceEvenly]])}}" aria-role="tablist"><view wx:for="{{tabs}}" wx:key="index" data-index="{{index}}" class="{{_.cls(classPrefix + '__item', [theme, ['evenly', spaceEvenly], placement, ['disabled', item.disabled], ['active', currentIndex === index]])}} {{currentIndex === index ? prefix + '-class-active' : ''}} {{prefix}}-class-item" bind:tap="onTabTap" aria-role="tab" aria-controls="{{tabID + '_panel_' + index}}" aria-selected="{{currentIndex === index}}" aria-disabled="{{item.disabled}}" aria-label="{{ ariaLabel || (item.badgeProps.dot || item.badgeProps.count ? item.label + _.getBadgeAriaLabel({ ...item.badgeProps }) : '') }}"><view class="{{_.cls(classPrefix + '__item-inner', [theme, ['active', currentIndex === index]])}}" aria-hidden="{{ item.badgeProps.dot || item.badgeProps.count }}"><template wx:if="{{item.icon}}" is="icon" data="{{ tClass: classPrefix + '__icon', ...item.icon }}"/><block wx:if="{{item.badgeProps}}"><template is="badge" data="{{ ...item.badgeProps, content: item.label, tClass: _.cls(classPrefix + '__badge', [ ['disabled', item.disabled], ['active', currentIndex === index]])}}"/></block><block wx:else>{{item.label}}</block></view><view wx:if="{{theme == 'card' && currentIndex - 1 == index}}" class="{{classPrefix}}__item-prefix"/><view wx:if="{{theme == 'card' && currentIndex + 1 == index}}" class="{{classPrefix}}__item-suffix"/></view><view wx:if="{{theme == 'line' && showBottomLine}}" class="{{_.cls(classPrefix + '__track', [placement])}} {{prefix}}-class-track" style="{{_tabs.trackStyle(trackOption)}}"/></view></scroll-view></view></t-sticky><slot name="middle"/><view class="{{_.cls(classPrefix + '__content', [['animated', animation]])}}" bind:touchstart="onTouchStart" bind:touchmove="onTouchMove" bind:touchend="onTouchEnd" bind:touchcancel="onTouchEnd"><view class="{{classPrefix}}__content-inner {{prefix}}-class-content" style="{{ _tabs.animate({duration: animation.duration, currentIndex:currentIndex}) }}"><slot/></view></view></view>