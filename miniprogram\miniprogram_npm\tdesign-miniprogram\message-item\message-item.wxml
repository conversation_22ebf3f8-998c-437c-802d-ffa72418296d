<wxs src="./message-item.wxs" module="_this"></wxs><wxs src="../common/utils.wxs" module="_"/><import src="../common/template/icon.wxml"/><block wx:if="{{visible}}"><view class="{{classPrefix}} class {{prefix}}-class {{classPrefix}}--{{theme}} {{fadeClass}}" style="{{_._style([_this.getMessageStyles(zIndex, offset, wrapTop), style, customStyle])}}" animation="{{showAnimation}}" id="{{id || classPrefix}}" aria-role="alert"><view class="{{classPrefix}}__icon--left"><slot name="icon"/><template wx:if="{{_icon}}" is="icon" data="{{tClass: prefix + '-class-icon', ariaHidden: true, ..._icon }}"/></view><view class="{{classPrefix}}__text-wrap {{marquee ? classPrefix + '__text-nowrap' : ''}}" style="text-align: {{align}}" id="{{classPrefix}}__text-wrap"><view class="{{classPrefix}}__text {{prefix}}-class-content" id="{{classPrefix}}__text" animation="{{animation}}"><block wx:if="{{content}}">{{content}}</block><slot name="content"/><slot/></view></view><t-link wx:if="{{_link && _link.content}}" class="{{classPrefix}}__link {{prefix}}-class-link" style="{{_._style([_link.style, _link.customStyle])}}" disabled="{{_link.disabled || false}}" hover="{{_link.hover || true}}" theme="{{_link.theme || 'primary'}}" size="{{_link.size || 'medium'}}" prefixIcon="{{_link.prefixIcon || false}}" suffixIcon="{{_link.suffixIcon || false}}" underline="{{_link.underline || false}}" content="{{_link.content || ''}}" navigatorProps="{{_link.navigatorProps || null}}" bind:complete="handleLinkClick"/><slot name="link"/><view class="{{classPrefix}}__icon--right" bind:tap="handleClose"><slot name="close-btn"/><template wx:if="{{_closeBtn}}" is="icon" data="{{tClass: prefix + '-class-close-btn', ariaRole: 'button', ariaLabel: '关闭', ..._closeBtn }}"/></view></view></block>