// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: 'cloud1-2g263bi11fd6ea28'
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event

  switch (action) {
    case 'sendMembershipNotification':
      return await sendMembershipNotification(data)
    case 'sendChatNotification':
      return await sendChatNotification(data)
    default:
      return {
        code: -1,
        message: '未知操作类型'
      }
  }
}

/**
 * 发送会员购买成功通知
 * @param {Object} data 通知数据
 */
async function sendMembershipNotification(data) {
  try {
    const { openid, templateId, page, agentTitle, planType, expireTime } = data

    if (!openid || !templateId) {
      return {
        code: -1,
        message: '缺少必要参数'
      }
    }

    // 发送订阅消息
    const result = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      templateId: templateId,
      page: page || 'pages/profile/profile',
      data: {
        // 以下字段需要根据实际的模板ID对应的字段进行修改
        thing1: {
          value: agentTitle || '智能体会员' // 会员名称
        },
        phrase2: {
          value: getPlanTypeText(planType) || '标准会员' // 会员类型
        },
        time3: {
          value: formatDate(expireTime) || '2023-12-31 23:59:59' // 到期时间
        },
        thing4: {
          value: '感谢您的购买，祝您使用愉快！' // 备注
        }
      }
    })

    return {
      code: 0,
      message: '发送成功',
      data: result
    }
  } catch (error) {
    console.error('发送订阅消息失败', error)
    return {
      code: -1,
      message: '发送失败: ' + error.message,
      error
    }
  }
}

/**
 * 发送聊天消息通知
 * @param {Object} data 通知数据
 */
async function sendChatNotification(data) {
  try {
    const { openid, templateId, page, agentName, messagePreview } = data

    if (!openid || !templateId) {
      return {
        code: -1,
        message: '缺少必要参数'
      }
    }

    // 发送订阅消息
    const result = await cloud.openapi.subscribeMessage.send({
      touser: openid,
      templateId: templateId,
      page: page || 'pages/chat/chat',
      data: {
        // 以下字段需要根据实际的模板ID对应的字段进行修改
        thing1: {
          value: agentName || '智能助手' // 智能体名称
        },
        thing2: {
          value: messagePreview || '您有一条新消息' // 消息预览
        },
        time3: {
          value: formatDate(new Date()) // 发送时间
        },
        thing4: {
          value: '点击查看详情' // 备注
        }
      }
    })

    return {
      code: 0,
      message: '发送成功',
      data: result
    }
  } catch (error) {
    console.error('发送聊天通知失败', error)
    return {
      code: -1,
      message: '发送失败: ' + error.message,
      error
    }
  }
}

/**
 * 获取会员类型的中文文本
 * @param {string} planType 会员类型
 * @returns {string} 会员类型文本
 */
function getPlanTypeText(planType) {
  switch (planType) {
    case 'monthly':
      return '月度会员'
    case 'quarterly':
      return '季度会员'
    case 'yearly':
      return '年度会员'
    default:
      return '标准会员'
  }
}

/**
 * 格式化日期为 YYYY-MM-DD HH:MM:SS 格式
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  if (!date) return ''
  
  const d = new Date(date)
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
} 