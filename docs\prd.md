# 智能体广场小程序 - 需求文档

** 1. 小程序介绍

**智能体广场**是一款允许用户使用我提供的所有 AI 智能体的小程序。

- 用户可以浏览并选择感兴趣的智能体进行交互。
- 每个用户都有自己的使用记录，可在“我的”页面查看历史智能体对话记录。

---

** 2. 功能总览

### 底部导航栏

- **首页**：浏览所有智能体  
- **我的**：用户登录、查看历史记录

---

## 3. 首页功能

- 展示我提供的所有智能体（图标卡片样式）
- 支持无限滚动加载（向下滑动自动加载更多）
- 每个智能体卡片包含：
  - 图标
  - 名称
  - 简要介绍
- 点击任意智能体卡片：
  - 跳转至该智能体的**介绍页面**

---

## 4. 智能体介绍页面

- 展示以下信息：
  - 智能体名称
  - 图标
  - 功能说明 / 应用场景
- 页面底部有一个按钮：**“开始对话”**
  - 点击跳转到该智能体的对话页

---

** 5. 智能体对话页面

- 类似 ChatGPT 的聊天界面
- 用户输入问题，AI 智能体作出回答
- 每一次对话自动记录到用户历史记录中

---

## 6. 我的页面

### 未登录状态：

- 自动弹出「微信一键登录」按钮
- 用户点击后可授权并完成登录

### 已登录状态：

- 显示用户微信头像和昵称
- 展示“历史记录”模块：
  - 用户使用过的智能体列表
  - 每项记录可点击跳转继续对话

---

## 7. 交互体验要求

- 风格简洁清爽，卡片式智能体展示
- 所有滚动加载需流畅不卡顿
- 所有按钮有点击反馈
- 页面跳转平滑，状态保持
- 聊天体验需对标 GPT 类对话交互标准

