<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><wxs src="./image-viewer.wxs" module="utils"/><view wx:if="{{visible}}" id="{{classPrefix}}" class="{{classPrefix}} class {{prefix}}-class" style="{{_._style([style, customStyle, '--td-image-viewer-top: ' + maskTop + 'px'])}}" aria-modal="{{true}}" aria-role="dialog" aria-label="图片查看器" catchtouchmove="true"><view class="{{classPrefix}}__mask" data-source="overlay" bind:tap="onClose" style="{{ 'background-color: ' + backgroundColor }}" aria-role="button" aria-label="关闭"/><block wx:if="{{images && images.length}}"><view class="{{classPrefix}}__content"><swiper class="swiper" style="{{swiperStyle[currentSwiperIndex].style}}" autoplay="{{false}}" current="{{currentSwiperIndex}}" bindchange="onSwiperChange" bindtap="onClose" tabindex="0"><swiper-item wx:for="{{images}}" wx:key="index" class="{{classPrefix}}__preview-image"><t-image wx:if="{{!lazy || utils.shouldLoadImage(index, currentSwiperIndex, loadedImageIndexes) }}" t-class="t-image--external" style="{{imagesStyle[index].style || ''}}" mode="aspectFit" src="{{item}}" data-index="{{index}}" class="{{classPrefix}}__image" bindload="onImageLoadSuccess"></t-image></swiper-item></swiper></view><view class="{{classPrefix}}__nav"><view class="{{classPrefix}}__nav-close" catch:tap="onClose" aria-role="button" aria-label="关闭"><slot name="close-btn"/><template wx:if="{{_closeBtn}}" is="icon" data="{{ ..._closeBtn }}"/></view><view wx:if="{{showIndex}}" class="{{classPrefix}}__nav-index">{{currentSwiperIndex + 1}}/{{images.length}}</view><view class="{{classPrefix}}__nav-delete" bind:tap="onDelete" aria-role="button" aria-label="删除"><slot name="delete-btn"/><template is="icon" data="{{ ..._deleteBtn }}"/></view></view></block></view>