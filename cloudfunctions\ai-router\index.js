// AI路由云函数 - 根据智能体自动选择Dify或Coze API
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: 'cloud1-2g263bi11fd6ea28'
})

// 智能体与AI服务的映射配置
const AGENT_AI_MAPPING = {
  '1': { service: 'coze', botId: '7497164550823755810' },        // 金恒AI小助理
  '2': { service: 'coze', botId: '7489009835820810275' },        // 金恒抖音爆款文案抓取仿写
  '3': { service: 'coze', botId: '7498997636667179062' },        // 金恒超级编导
  '4': { service: 'coze', botId: '7493071819755372579' },        // 金恒抖音账号分析师
  '5': { service: 'coze', botId: '7507539586646802495' },        // 金恒爆款小红书图文生成助手
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  
  console.log('=== AI路由云函数启动 ===')
  console.log('Action:', action)
  console.log('Data:', JSON.stringify(data, null, 2))
  console.log('当前时间:', new Date().toISOString())
  
  try {
    // 根据智能体ID获取AI服务配置
    const agentId = data.agentId || data.agent_id
    const agentConfig = AGENT_AI_MAPPING[agentId]
    
    if (!agentConfig) {
      console.error('❌ 未找到智能体配置:', agentId)
      console.error('可用的智能体ID:', Object.keys(AGENT_AI_MAPPING))
      return {
        success: false,
        error: `未找到智能体 ${agentId} 的配置`
      }
    }

    console.log('✅ 找到智能体配置:', agentConfig)
    console.log('将使用AI服务:', agentConfig.service)
    
    // 根据AI服务类型路由到对应的处理函数
    if (agentConfig.service === 'dify') {
      return await handleDifyRequest(action, data, agentConfig)
    } else if (agentConfig.service === 'coze') {
      return await handleCozeRequest(action, data, agentConfig)
    } else {
      return {
        success: false,
        error: `不支持的AI服务类型: ${agentConfig.service}`
      }
    }
    
  } catch (error) {
    console.error('AI路由处理失败:', error)
    return {
      success: false,
      error: error.message || 'AI路由处理失败'
    }
  }
}

// 处理Dify请求
async function handleDifyRequest(action, data, agentConfig) {
  console.log('=== 路由到Dify API ===')
  
  try {
    // 调用Dify云函数
    const difyResult = await cloud.callFunction({
      name: 'dify',
      data: {
        action: action,
        data: {
          ...data,
          agentId: agentConfig.botId // 使用配置的botId
        }
      }
    })
    
    console.log('Dify云函数响应:', difyResult.result)
    return difyResult.result
    
  } catch (error) {
    console.error('调用Dify云函数失败:', error)
    return {
      success: false,
      error: `Dify API调用失败: ${error.message}`
    }
  }
}

// 处理Coze请求
async function handleCozeRequest(action, data, agentConfig) {
  console.log('=== 路由到Coze API ===')
  
  try {
    // 调用Coze云函数
    const cozeResult = await cloud.callFunction({
      name: 'coze',
      data: {
        action: action,
        data: {
          ...data,
          agentId: agentConfig.botId // 使用配置的botId
        }
      },
      timeout: 50000 // 50秒超时
    })
    
    console.log('Coze云函数响应:', cozeResult.result)
    
    // 将Coze的响应格式转换为统一格式
    return normalizeCozeResponse(cozeResult.result, action)
    
  } catch (error) {
    console.error('调用Coze云函数失败:', error)
    return {
      success: false,
      error: `Coze API调用失败: ${error.message}`
    }
  }
}

// 将Coze响应格式标准化为Dify格式
function normalizeCozeResponse(cozeResponse, action) {
  if (!cozeResponse.success) {
    return cozeResponse
  }
  
  switch (action) {
    case 'createConversation':
      return {
        success: true,
        conversation_id: cozeResponse.conversation_id,
        welcome_message: cozeResponse.welcome_message || '你好！我是AI助手，有什么可以帮助你的吗？'
      }
      
    case 'sendMessage':
      return {
        success: true,
        message_id: cozeResponse.message_id,
        conversation_id: cozeResponse.conversation_id,
        answer: cozeResponse.answer,
        created_at: cozeResponse.created_at,
        status: cozeResponse.status || 'completed'
      }
      
    case 'pollMessage':
      return {
        success: true,
        message: {
          answer: cozeResponse.data?.content || cozeResponse.data?.answer || '',
          status: cozeResponse.data?.status || 'completed'
        }
      }
      
    case 'getConversationHistory':
      return {
        success: true,
        data: cozeResponse.data || [],
        has_more: cozeResponse.has_more || false
      }
      
    default:
      return cozeResponse
  }
}
