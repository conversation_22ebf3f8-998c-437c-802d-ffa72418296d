<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><wxs src="./progress.wxs" module="_this"/><view style="{{_._style([style, customStyle])}}" class="{{classPrefix}} class"><view wx:if="{{theme === _this.PRO_THEME.LINE}}" class="{{classPrefix }}--thin {{classPrefix}}--status--{{status || computedStatus}} {{prefix}}-class"><view aria-role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="{{computedProgress}}" aria-label="{{ ariaLabel || (isIOS ? _this.getIOSAriaLabel(status) : _this.getAndroidAriaLabel(status))  }}" aria-live="polite" class="{{classPrefix }}__bar" style="height: {{heightBar}}px;border-radius: {{heightBar}}px;background-color: {{bgColorBar}}"><view class="{{classPrefix }}__inner {{prefix}}-class-bar" style="background: {{colorBar}}; width: {{computedProgress + '%'}}"></view></view><view wx:if="{{label}}" class="{{classPrefix}}__info {{prefix}}-class-label" aria-hidden="{{ true }}"><template wx:if="{{_.includes(_this.STATUS, status)}}" is="icon" data="{{tClass: classPrefix + '__icon', size:'44rpx', name: _this.LINE_STATUS_ICON[status]}}"></template><text wx:else>{{ _.isString(label)? label: computedProgress + '%' }}</text></view><slot name="label"/></view><view aria-role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="{{computedProgress}}" aria-label="{{ ariaLabel || (isIOS ? _this.getIOSAriaLabel(status) : _this.getAndroidAriaLabel(status))  }}" aria-live="polite" wx:if="{{theme === _this.PRO_THEME.PLUMP}}" class="{{classPrefix}}__bar {{classPrefix}}--plump {{computedProgress > 10 ? classPrefix + '--over-ten': classPrefix + '--under-ten'}} {{classPrefix}}--status--{{status || computedStatus}} {{prefix}}-class" style="height: {{heightBar}}px;border-radius: {{heightBar}}px;background-color: {{bgColorBar}}"><view class="{{classPrefix}}__inner {{prefix}}-class-bar" style="background: {{colorBar}}; width: {{computedProgress}}%"><view wx:if="{{label && computedProgress > 10}}" class="{{classPrefix }}__info {{prefix}}-class-label"><text>{{ _.isString(label)? label: computedProgress + '%' }}</text></view><slot wx:if="{{computedProgress > 10}}" name="label"/></view><view wx:if="{{label && computedProgress <= 10}}" class="{{ classPrefix }}__info {{prefix}}-class-label" aria-hidden="{{ true }}"><text>{{ _.isString(label)? label: computedProgress + '%' }}</text></view><slot wx:if="{{computedProgress <= 10}}" name="label"/></view><view wx:if="{{theme === _this.PRO_THEME.CIRCLE}}" class="{{classPrefix}}--status--{{status || computedStatus}} {{prefix}}-class"><view aria-role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="{{computedProgress}}" aria-label="{{ ariaLabel || (isIOS ? _this.getIOSAriaLabel(status) : _this.getAndroidAriaLabel(status))  }}" aria-live="polite" class="{{_.cls(classPrefix + '__canvas--circle', [[size, true]])}}" style="{{_this.getCircleStyle(size, heightBar)}}; background-image: conic-gradient(from var(--td-progress-circle-from), {{colorCircle || _this.STATUS_COLOR[status] || 'var(--td-progress-inner-bg-color)'}} {{computedProgress}}%, {{bgColorBar || 'var(--td-progress-track-bg-color)'}} 0%);"><view class="{{classPrefix}}__canvas--inner {{prefix}}-class-bar"><view wx:if="{{label}}" class="{{classPrefix}}__info {{prefix}}-class-label" aria-hidden="{{ true }}"><template wx:if="{{_.includes(_this.STATUS, status)}}" is="icon" data="{{tClass: classPrefix + '__icon', size:'96rpx', name: _this.CIRCLE_STATUS_ICON[status]}}"></template><text wx:else>{{ _.isString(label)? label: computedProgress + '%' }}</text></view><slot name="label"/></view></view></view></view>