import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";import config from"../common/config";import{calcIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-result`,THEME_ICON={default:"error-circle",success:"check-circle",warning:"error-circle",error:"close-circle"};let default_1=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`,`${prefix}-class-image`,`${prefix}-class-title`,`${prefix}-class-description`],this.properties=props,this.data={prefix:prefix,classPrefix:name},this.lifetimes={ready(){this.initIcon()}},this.observers={"icon, theme"(){this.initIcon()}},this.methods={initIcon(){const{icon:e,theme:o}=this.properties;this.setData({_icon:calcIcon(e,THEME_ICON[o])})}}}};default_1=__decorate([wxComponent()],default_1);export default default_1;