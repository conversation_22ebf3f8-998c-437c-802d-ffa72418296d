import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{uniqueFactory}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-badge`,getUniqueID=uniqueFactory("badge");let Badge=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`,`${prefix}-class-count`,`${prefix}-class-content`],this.properties=props,this.data={prefix:prefix,classPrefix:name,value:"",labelID:"",descriptionID:""},this.lifetimes={ready(){const e=getUniqueID();this.setData({labelID:`${e}_label`,descriptionID:`${e}_description`})}}}};Badge=__decorate([wxComponent()],Badge);export default Badge;