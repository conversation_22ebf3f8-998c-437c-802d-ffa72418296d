// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: 'cloud1-2g263bi11fd6ea28'
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const { DIFY_API_KEY, DIFY_API_BASE_URL } = process.env

  // 从环境变量或数据中获取API配置
  const apiKey = DIFY_API_KEY || data?.apiKey
  const apiBaseUrl = DIFY_API_BASE_URL || data?.apiBaseUrl || 'https://api.dify.ai/v1'

  // 如果没有API密钥，返回错误
  if (!apiKey) {
    console.error('DIFY_API_KEY not configured in environment or data')
    return {
      success: false,
      error: 'DIFY_API_KEY not configured. Please set it in cloud function environment variables or pass it in data.'
    }
  }

  console.log('使用的Dify API地址:', apiBaseUrl)
  console.log('API Key前4位:', apiKey.substring(0, 4) + '...')
  
  // 配置axios请求头
  const axiosConfig = {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    // 将超时设置为50秒，给AI回复足够的时间
    timeout: 50000
  }

  try {
    // 根据不同的操作执行不同的功能
    switch (action) {
      // 获取API配置（新增）
      case 'getApiConfig':
        return {
          success: true,
          api_key: apiKey,
          api_base_url: apiBaseUrl
        }

      // 测试连接（新增）
      case 'testConnection':
        try {
          console.log('=== 测试Dify API连接 ===')
          console.log('测试URL:', `${apiBaseUrl}/chat-messages`)
          console.log('API Key前4位:', apiKey.substring(0, 4) + '...')

          // 发送一个简单的测试请求
          const testResponse = await axios.post(
            `${apiBaseUrl}/chat-messages`,
            {
              query: "Hello",
              user: "test_user",
              response_mode: "blocking"
            },
            axiosConfig
          )

          console.log('测试请求成功，状态码:', testResponse.status)
          return {
            success: true,
            message: 'Dify API连接测试成功',
            status_code: testResponse.status,
            api_url: `${apiBaseUrl}/chat-messages`
          }
        } catch (testError) {
          console.error('测试连接失败:', testError.message)
          console.error('错误详情:', testError.response?.data)
          return {
            success: false,
            error: testError.message,
            error_code: testError.response?.status || 500,
            error_data: testError.response?.data,
            api_url: `${apiBaseUrl}/chat-messages`
          }
        }

      // 创建新对话
      case 'createConversation':
        // 不再生成自定义ID，而是返回一个标记，表示需要创建新对话
        return {
          success: true,
          conversation_id: 'new', // 使用'new'标记表示需要创建新对话
          welcome_message: '你好！有什么我可以帮助你的吗？'
        }

      // 发送消息
      case 'sendMessage':
        const { conversationId, message, agentId: messageAgentId, user = 'default_user', inputs = {}, streaming = false } = data

        console.log('=== 发送消息开始 ===')
        console.log('对话ID:', conversationId)
        console.log('消息内容:', message)
        console.log('用户ID:', user)
        console.log('流式响应:', streaming)

        // 构造请求体
        const requestBody = {
          query: message,
          user: user,
          inputs: inputs, // 添加inputs参数，即使是空对象
          response_mode: streaming ? 'streaming' : 'blocking' // 根据参数选择响应模式
        }

        // 只有当conversationId不是'new'时，才添加conversation_id字段
        if (conversationId !== 'new') {
          requestBody.conversation_id = conversationId
        }

        // 如果有文件，添加到请求中
        if (data.files && data.files.length > 0) {
          requestBody.files = data.files
        }

        console.log('发送消息请求体:', JSON.stringify(requestBody, null, 2))
        console.log('请求URL:', `${apiBaseUrl}/chat-messages`)
        console.log('请求头:', JSON.stringify(axiosConfig.headers, null, 2))

        try {
          console.log('发送请求到Dify API:', `${apiBaseUrl}/chat-messages`)

          // 根据响应模式调整axios配置
          const requestConfig = { ...axiosConfig }
          if (streaming) {
            // 对于流式响应，设置responseType为stream
            requestConfig.responseType = 'text'
            requestConfig.headers = {
              ...requestConfig.headers,
              'Accept': 'text/event-stream'
            }
          }

          // 发送请求到Dify API
          const response = await axios.post(
            `${apiBaseUrl}/chat-messages`,
            requestBody,
            requestConfig
          )

          console.log('=== Dify API响应 ===')
          console.log('响应状态码:', response.status)
          console.log('响应头:', JSON.stringify(response.headers, null, 2))

          // 根据响应类型打印不同的日志
          if (typeof response.data === 'string') {
            console.log('响应数据类型: string')
            console.log('响应数据长度:', response.data.length)
            console.log('响应数据前500字符:', response.data.substring(0, 500))
          } else {
            console.log('响应数据类型: object')
            console.log('响应数据:', JSON.stringify(response.data, null, 2))
          }
          
          let messageId = null
          let respConversationId = null
          
          // 根据响应模式处理不同的响应格式
          if (streaming) {
            console.log('=== 处理流式响应 ===')
            // 在流式响应中，响应是SSE格式的字符串，需要解析
            const responseText = response.data

            if (typeof responseText === 'string' && responseText.includes('data:')) {
              console.log('检测到SSE格式响应，开始解析...')
              // 从第一个数据块中提取 message_id 和 conversation_id
              try {
                // 寻找第一个包含message_id或id的数据块
                const chunks = responseText.split('\n\n');
                console.log('SSE数据块数量:', chunks.length)
                let foundMessageId = false;
                let fullAnswer = '';
                let isCompleted = false;

                for (let i = 0; i < chunks.length; i++) {
                  const chunk = chunks[i];
                  if (!chunk.trim() || !chunk.startsWith('data:')) continue;

                  const jsonStr = chunk.replace('data:', '').trim();
                  if (!jsonStr) continue;

                  console.log(`解析第${i+1}个数据块:`, jsonStr.substring(0, 200))

                  try {
                    const chunkData = JSON.parse(jsonStr);

                    // 获取消息ID（可能在message_id或id字段中）
                    if (chunkData.message_id || chunkData.id) {
                      messageId = chunkData.message_id || chunkData.id;
                      foundMessageId = true;
                      console.log('找到消息ID:', messageId);
                    }
                    // 也可能在data字段下
                    else if (chunkData.data && (chunkData.data.message_id || chunkData.data.id)) {
                      messageId = chunkData.data.message_id || chunkData.data.id;
                      foundMessageId = true;
                      console.log('从data字段找到消息ID:', messageId);
                    }

                    // 获取会话ID
                    if (chunkData.conversation_id) {
                      respConversationId = chunkData.conversation_id;
                      console.log('找到对话ID:', respConversationId);
                    }
                    // 会话ID也可能在data字段下
                    else if (chunkData.data && chunkData.data.conversation_id) {
                      respConversationId = chunkData.data.conversation_id;
                      console.log('从data字段找到对话ID:', respConversationId);
                    }

                    // 累积回答内容
                    if (chunkData.answer) {
                      fullAnswer += chunkData.answer;
                      console.log('累积回答内容:', chunkData.answer);
                      console.log('累积回答总长度:', fullAnswer.length);
                      console.log('当前累积内容:', fullAnswer);
                    }

                    // 检查是否完成
                    if (chunkData.event === 'message_end' || chunkData.event === 'workflow_finished') {
                      isCompleted = true;
                      console.log('检测到流式响应完成事件:', chunkData.event);
                    }
                  } catch (innerError) {
                    console.error('解析数据块失败:', innerError.message, '数据块:', jsonStr.substring(0, 100));
                  }
                }

                console.log('从流式响应中提取的消息ID:', messageId);
                console.log('从流式响应中提取的对话ID:', respConversationId);
                console.log('累积的完整回答:', fullAnswer);
                console.log('是否已完成:', isCompleted);

                // 如果流式响应已经完成，直接返回完整结果
                if (isCompleted && fullAnswer) {
                  console.log('流式响应已完成，直接返回结果');
                  return {
                    success: true,
                    message_id: messageId,
                    conversation_id: respConversationId || conversationId,
                    answer: fullAnswer,
                    created_at: Math.floor(Date.now() / 1000),
                    status: 'completed'
                  };
                }
              } catch (parseError) {
                console.error('解析流式响应失败:', parseError.message);
              }
            } else if (typeof responseText === 'object') {
              console.log('流式响应返回了对象格式，直接提取ID')
              messageId = responseText.message_id || responseText.id
              respConversationId = responseText.conversation_id
            } else {
              console.error('未知的流式响应格式:', typeof responseText)
            }
          } else {
            console.log('=== 处理阻塞响应 ===')
            // 阻塞模式返回标准的JSON对象
            messageId = response.data.message_id || response.data.id
            respConversationId = response.data.conversation_id
            console.log('阻塞模式消息ID:', messageId)
            console.log('阻塞模式对话ID:', respConversationId)
          }
          
          console.log('=== 验证和返回结果 ===')
          console.log('最终消息ID:', messageId)
          console.log('最终对话ID:', respConversationId)

          // 如果没有消息ID，尝试生成一个临时ID
          if (!messageId) {
            console.warn('Dify API未返回message_id，生成临时ID')
            messageId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          }

          // 返回处理后的响应
          if (streaming) {
            console.log('=== 返回流式响应结果 ===')
            // 对于流式响应，返回累积的完整答案
            // 从前面的解析中，我们已经累积了完整的答案在fullAnswer变量中
            let finalAnswer = "正在生成回复..."

            // 尝试从流式响应中获取累积的完整答案
            try {
              const responseText = response.data
              if (typeof responseText === 'string') {
                const chunks = responseText.split('\n\n')
                let accumulatedAnswer = ''

                for (const chunk of chunks) {
                  if (chunk.includes('"answer":')) {
                    const jsonStr = chunk.replace('data:', '').trim()
                    try {
                      const chunkData = JSON.parse(jsonStr)
                      if (chunkData.answer) {
                        accumulatedAnswer += chunkData.answer
                      }
                    } catch (parseError) {
                      console.log('跳过无效的数据块:', jsonStr.substring(0, 100))
                    }
                  }
                }

                if (accumulatedAnswer) {
                  finalAnswer = accumulatedAnswer
                  console.log('累积的完整回复:', finalAnswer)
                  console.log('累积回复长度:', finalAnswer.length)
                }
              } else if (responseText.answer) {
                finalAnswer = responseText.answer
                console.log('从对象响应中提取回复:', finalAnswer)
              }
            } catch (e) {
              console.error('提取完整回复失败:', e.message)
            }

            const streamingResult = {
              success: true,
              message_id: messageId,
              conversation_id: respConversationId || conversationId,
              answer: finalAnswer,
              created_at: Math.floor(Date.now() / 1000),
              status: 'completed'  // 设置为completed，表示流式响应已完成
            }
            console.log('流式响应返回结果:', JSON.stringify(streamingResult, null, 2))
            console.log('流式响应最终答案长度:', finalAnswer.length)
            return streamingResult
          } else {
            console.log('=== 返回阻塞响应结果 ===')
            console.log('原始响应数据结构:', JSON.stringify(response.data, null, 2))

            // 对于阻塞模式，尝试多种方式获取回复内容
            let answer = '抱歉，未收到有效回复'

            // 尝试从不同字段获取答案
            if (response.data.answer) {
              answer = response.data.answer
              console.log('从answer字段获取内容:', answer)
            } else if (response.data.content) {
              answer = response.data.content
              console.log('从content字段获取内容:', answer)
            } else if (response.data.data && response.data.data.answer) {
              answer = response.data.data.answer
              console.log('从data.answer字段获取内容:', answer)
            } else if (response.data.result && response.data.result.answer) {
              answer = response.data.result.answer
              console.log('从result.answer字段获取内容:', answer)
            } else {
              console.log('未找到有效的回复内容，响应数据:', response.data)
            }

            console.log('最终回复内容:', answer)
            console.log('最终回复内容长度:', answer.length)

            const blockingResult = {
              success: true,
              message_id: messageId,
              conversation_id: respConversationId || conversationId,
              answer: answer,
              created_at: response.data.created_at || Math.floor(Date.now() / 1000),
              status: 'completed'
            }
            console.log('阻塞响应返回结果:', JSON.stringify(blockingResult, null, 2))
            return blockingResult
          }
        } catch (sendError) {
          console.error('=== 发送消息失败 ===')
          console.error('错误类型:', sendError.name)
          console.error('错误消息:', sendError.message)
          console.error('错误代码:', sendError.code)
          console.error('响应状态:', sendError.response?.status)
          console.error('响应数据:', sendError.response?.data)
          console.error('请求配置:', {
            url: sendError.config?.url,
            method: sendError.config?.method,
            headers: sendError.config?.headers
          })

          // 根据错误类型返回不同的错误信息
          let errorMessage = '抱歉，我暂时无法回答您的问题。'
          if (sendError.code === 'ECONNREFUSED') {
            errorMessage = '无法连接到Dify服务器，请检查服务器地址是否正确。'
          } else if (sendError.code === 'ETIMEDOUT') {
            errorMessage = '请求超时，服务器响应时间过长。'
          } else if (sendError.response?.status === 401) {
            errorMessage = 'API密钥无效，请检查配置。'
          } else if (sendError.response?.status === 404) {
            errorMessage = 'API接口不存在，请检查服务器地址。'
          } else if (sendError.response?.status >= 500) {
            errorMessage = '服务器内部错误，请稍后再试。'
          }

          // 返回友好的错误信息
          const errorResult = {
            success: false,
            error: sendError.message || 'Unknown error',
            error_code: sendError.response?.status || 500,
            message_id: `error_${Date.now()}`,
            conversation_id: conversationId,
            answer: errorMessage,
            created_at: Math.floor(Date.now() / 1000),
            status: 'error'
          }
          console.log('错误响应返回结果:', JSON.stringify(errorResult, null, 2))
          return errorResult
        }

      // 轮询消息更新
      case 'pollMessage':
        const { messageId } = data
        
        if (!messageId) {
          return {
            success: false,
            error: 'Missing messageId',
            message: null,
            answer: '抱歉，我暂时无法回答您的问题。请稍后再试。',
            status: 'error'
          }
        }
        
        // 检查 messageId 是否有效（不是以 error_ 开头）
        if (messageId.startsWith('error_') || messageId === 'undefined' || messageId === 'null') {
          console.error('无效的消息ID:', messageId);
          return {
            success: false,
            error: 'Invalid messageId',
            message: null,
            answer: '抱歉，我暂时无法回答您的问题。请稍后再试。',
            status: 'error'
          }
        }
        
        try {
          console.log('轮询消息，ID:', messageId);
          
          // 构建请求配置，增加接收类型以明确需要JSON响应
          const pollConfig = {
            ...axiosConfig,
            headers: {
              ...axiosConfig.headers,
              'Accept': 'application/json'
            }
          };
          
          // 获取消息详情
          const messageResponse = await axios.get(
            `${DIFY_API_BASE_URL}/messages/${messageId}`,
            pollConfig
          )
          
          // 打印响应内容
          if (typeof messageResponse.data === 'string') {
            console.log('Dify API消息响应(字符串格式，前100字符):', messageResponse.data.substring(0, 100));
          } else {
            console.log('Dify API消息响应:', JSON.stringify(messageResponse.data));
          }
          
          // 检查响应格式
          if (typeof messageResponse.data === 'string') {
            // 如果响应是字符串，可能是SSE格式，尝试解析
            try {
              // 尝试将其作为JSON解析
              const jsonData = JSON.parse(messageResponse.data);
              return {
                success: true,
                message: jsonData,
                answer: jsonData.answer || jsonData.content || '',
                status: jsonData.status || 'completed',
                created_at: jsonData.created_at || Math.floor(Date.now() / 1000),
                updated_at: jsonData.updated_at || Math.floor(Date.now() / 1000)
              }
            } catch (jsonError) {
              console.error('解析响应为JSON失败，尝试解析SSE格式:', jsonError);
              
              // 尝试从SSE格式中提取数据
              const chunks = messageResponse.data.split('\n\n');
              let lastMessageChunk = null;
              let messageEndChunk = null;
              let fullAnswer = '';
              
              // 遍历所有数据块，拼接完整回答
              for (const chunk of chunks) {
                if (!chunk.trim() || !chunk.startsWith('data:')) continue;
                
                try {
                  const jsonStr = chunk.replace('data:', '').trim();
                  if (!jsonStr) continue;
                  
                  const chunkData = JSON.parse(jsonStr);
                  
                  // 记录最后一个消息块
                  if (chunkData.event === 'message') {
                    lastMessageChunk = chunkData;
                    
                    // 累积回答内容
                    if (chunkData.answer) {
                      fullAnswer = chunkData.answer;
                    }
                  } 
                  // 找到消息结束块
                  else if (chunkData.event === 'message_end') {
                    messageEndChunk = chunkData;
                    
                    // 通常结束块会包含完整回答
                    if (chunkData.answer) {
                      fullAnswer = chunkData.answer;
                    }
                    break; // 找到消息结束块后停止
                  }
                } catch (parseError) {
                  console.error('解析SSE数据块失败:', parseError, '数据块:', chunk.substring(0, 100));
                  // 继续处理下一个块
                }
              }
              
              // 优先使用消息结束块
              const targetChunk = messageEndChunk || lastMessageChunk;
              
              if (targetChunk) {
                // 确定状态
                const isCompleted = messageEndChunk !== null;
                
                return {
                  success: true,
                  message: targetChunk,
                  answer: fullAnswer || targetChunk.answer || '',
                  status: isCompleted ? 'completed' : 'processing',
                  created_at: targetChunk.created_at || Math.floor(Date.now() / 1000),
                  updated_at: Math.floor(Date.now() / 1000)
                }
              } else {
                console.error('在SSE响应中未找到有效消息块');
                throw new Error('No valid message chunks found in SSE response');
              }
            }
          }
          
          // 检查响应中是否包含必要的字段
          if (!messageResponse.data) {
            return {
              success: false,
              error: 'Empty response from Dify API',
              message: null,
              answer: '抱歉，服务器返回了空响应，请稍后再试。',
              status: 'error'
            }
          }
          
          // 返回消息详情
          return {
            success: true,
            message: messageResponse.data,
            answer: messageResponse.data.answer || messageResponse.data.content || '',
            status: messageResponse.data.status || 'completed',
            created_at: messageResponse.data.created_at || Math.floor(Date.now() / 1000),
            updated_at: messageResponse.data.updated_at || Math.floor(Date.now() / 1000)
          }
        } catch (pollError) {
          console.error('轮询消息失败:', pollError);
          console.error('详细错误信息:', pollError.response?.data || '无详细信息');
          
          // 检查是否是404错误（消息不存在）
          if (pollError.response && pollError.response.status === 404) {
            return {
              success: false,
              error: 'Message not found',
              error_code: 404,
              message: null,
              answer: '抱歉，未找到相关消息记录。',
              status: 'error'
            }
          }
          
          return {
            success: false,
            error: pollError.message || 'Unknown error',
            error_code: pollError.response?.status || 500,
            message: null,
            answer: '抱歉，我暂时无法获取回复内容。请稍后再试。',
            status: 'error'
          }
        }

      // 获取对话历史
      case 'getConversationHistory':
        const { conversationId: historyConvId, firstMessageId } = data
        
        // 构造请求URL和参数
        let url = `${DIFY_API_BASE_URL}/messages?conversation_id=${historyConvId}`
        if (firstMessageId) {
          url += `&first_id=${firstMessageId}`
        }

        try {
          // 发送请求到Dify API
          const historyResponse = await axios.get(url, axiosConfig)
          
          return {
            success: true,
            data: historyResponse.data.data || [],
            has_more: historyResponse.data.has_more || false
          }
        } catch (historyError) {
          console.error('获取对话历史失败:', historyError)
          return {
            success: false,
            error: historyError.message,
            error_code: historyError.response?.status || 500,
            data: [],
            has_more: false
          }
        }

      // 上传文件
      case 'uploadFile':
        try {
          const { fileContent, fileName, fileType, user: fileUser = 'default_user' } = data
          
          // 由于云函数环境中没有FormData和Blob对象，我们需要使用不同的方法
          // 使用multipart/form-data格式手动构建请求
          
          // 生成一个随机的边界字符串
          const boundary = '----WebKitFormBoundary' + Math.random().toString(16).substr(2)
          
          // 构建multipart/form-data请求体
          let requestBody = ''
          
          // 添加文件
          requestBody += `--${boundary}\r\n`
          requestBody += `Content-Disposition: form-data; name="file"; filename="${fileName}"\r\n`
          requestBody += `Content-Type: ${fileType}\r\n\r\n`
          
          // 文件内容需要是Buffer类型
          const fileBuffer = Buffer.from(fileContent)
          
          // 添加用户ID
          requestBody += `\r\n--${boundary}\r\n`
          requestBody += `Content-Disposition: form-data; name="user"\r\n\r\n`
          requestBody += `${fileUser}\r\n`
          requestBody += `--${boundary}--\r\n`
          
          // 创建请求配置
          const uploadConfig = {
            headers: {
              'Authorization': `Bearer ${DIFY_API_KEY}`,
              'Content-Type': `multipart/form-data; boundary=${boundary}`,
              'Content-Length': Buffer.byteLength(requestBody) + fileBuffer.length
            },
            timeout: 50000 // 保持与其他请求一致的超时时间
          }
          
          // 使用axios发送请求
          const uploadResponse = await axios.post(
            `${DIFY_API_BASE_URL}/files/upload`,
            Buffer.concat([Buffer.from(requestBody, 'utf8'), fileBuffer, Buffer.from(`\r\n--${boundary}--\r\n`)]),
            uploadConfig
          )
          
          return {
            success: true,
            file_id: uploadResponse.data.id,
            file_name: uploadResponse.data.name
          }
        } catch (uploadError) {
          console.error('文件上传错误:', uploadError)
          return {
            success: false,
            error: uploadError.message,
            error_code: uploadError.response?.status || 500,
            file_id: null,
            file_name: null
          }
        }

      // 获取建议问题
      case 'getSuggestedQuestions':
        const { messageId: suggestionMessageId, user: questionUser = 'default_user' } = data
        
        // 检查 messageId 是否有效（不是以 error_ 开头）
        if (!suggestionMessageId || suggestionMessageId.startsWith('error_')) {
          return {
            success: false,
            error: 'Invalid messageId',
            suggestions: [], // 返回空数组
            hasSuggestions: false
          }
        }
        
        try {
          // 发送请求到Dify API
          const suggestionsResponse = await axios.get(
            `${DIFY_API_BASE_URL}/messages/${suggestionMessageId}/suggested?user=${questionUser}`,
            axiosConfig
          )
          
          const suggestions = suggestionsResponse.data.data || [];
          
          return {
            success: true,
            suggestions: suggestions,
            hasSuggestions: suggestions.length > 0
          }
        } catch (suggestionError) {
          console.error('获取建议问题失败:', suggestionError)
          // 获取失败时返回错误信息
          return {
            success: false,
            error: suggestionError.message,
            error_code: suggestionError.response?.status || 500,
            suggestions: [], // 返回空数组
            hasSuggestions: false
          }
        }

      default:
        return {
          success: false,
          error: `Unknown action: ${action}`
        }
    }
  } catch (error) {
    console.error('Dify API Error:', error)
    
    // 返回格式化的错误信息
    return {
      success: false,
      error: error.message,
      error_code: error.response?.status || 500,
      data: error.response?.data || null
    }
  }
} 