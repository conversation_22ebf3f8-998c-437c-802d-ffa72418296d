import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{calcIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-dropdown-menu`;let DropdownMenu=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-item`,`${prefix}-class-label`,`${prefix}-class-icon`],this.properties=props,this.nodes=null,this.data={prefix:prefix,classPrefix:name,menus:null,activeIdx:-1,bottom:0,_arrowIcon:{name:props.arrowIcon.value}},this.relations={"../dropdown-item/dropdown-item":{type:"child"}},this.lifetimes={ready(){this.getAllItems()}},this.observers={arrowIcon(e){this.setData({_arrowIcon:calcIcon(e)})},activeIdx(e){this.triggerEvent(-1===e?"close":"open")}},this.methods={toggle(e){const{activeIdx:t,duration:o}=this.data,s=this.$children[t],r=this.$children[e];(null==r?void 0:r.data.disabled)||(-1!==t&&(s.triggerEvent("close"),s.setData({show:!1},(()=>{setTimeout((()=>{s.triggerEvent("closed")}),o)}))),null==e||t===e?this.setData({activeIdx:-1}):(r.triggerEvent("open"),this.setData({activeIdx:e}),r.setData({show:!0},(()=>{setTimeout((()=>{r.triggerEvent("opened")}),o)}))))},getAllItems(){const e=this.$children.map((({data:e})=>({label:e.label||e.computedLabel,disabled:e.disabled})));this.setData({menus:e})},handleToggle(e){const{index:t}=e.currentTarget.dataset;this.toggle(t)},noop(){}}}};DropdownMenu=__decorate([wxComponent()],DropdownMenu);export default DropdownMenu;