function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}var trimLeft=/^\s+/,trimRight=/\s+$/;function tinycolor(t,r){if(r=r||{},(t=t||"")instanceof tinycolor)return t;if(!(this instanceof tinycolor))return new tinycolor(t,r);var e=inputToRGB(t);this._originalInput=t,this._r=e.r,this._g=e.g,this._b=e.b,this._a=e.a,this._roundA=Math.round(100*this._a)/100,this._format=r.format||e.format,this._gradientType=r.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=e.ok}function inputToRGB(t){var r={r:0,g:0,b:0},e=1,n=null,a=null,o=null,i=!1,s=!1;return"string"==typeof t&&(t=stringInputToObject(t)),"object"==_typeof(t)&&(isValidCSSUnit(t.r)&&isValidCSSUnit(t.g)&&isValidCSSUnit(t.b)?(r=rgbToRgb(t.r,t.g,t.b),i=!0,s="%"===String(t.r).substr(-1)?"prgb":"rgb"):isValidCSSUnit(t.h)&&isValidCSSUnit(t.s)&&isValidCSSUnit(t.v)?(n=convertToPercentage(t.s),a=convertToPercentage(t.v),r=hsvToRgb(t.h,n,a),i=!0,s="hsv"):isValidCSSUnit(t.h)&&isValidCSSUnit(t.s)&&isValidCSSUnit(t.l)&&(n=convertToPercentage(t.s),o=convertToPercentage(t.l),r=hslToRgb(t.h,n,o),i=!0,s="hsl"),t.hasOwnProperty("a")&&(e=t.a)),e=boundAlpha(e),{ok:i,format:t.format||s,r:Math.min(255,Math.max(r.r,0)),g:Math.min(255,Math.max(r.g,0)),b:Math.min(255,Math.max(r.b,0)),a:e}}function rgbToRgb(t,r,e){return{r:255*bound01(t,255),g:255*bound01(r,255),b:255*bound01(e,255)}}function rgbToHsl(t,r,e){t=bound01(t,255),r=bound01(r,255),e=bound01(e,255);var n,a,o=Math.max(t,r,e),i=Math.min(t,r,e),s=(o+i)/2;if(o==i)n=a=0;else{var h=o-i;switch(a=s>.5?h/(2-o-i):h/(o+i),o){case t:n=(r-e)/h+(r<e?6:0);break;case r:n=(e-t)/h+2;break;case e:n=(t-r)/h+4}n/=6}return{h:n,s:a,l:s}}function hslToRgb(t,r,e){var n,a,o;function i(t,r,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(r-t)*e:e<.5?r:e<2/3?t+(r-t)*(2/3-e)*6:t}if(t=bound01(t,360),r=bound01(r,100),e=bound01(e,100),0===r)n=a=o=e;else{var s=e<.5?e*(1+r):e+r-e*r,h=2*e-s;n=i(h,s,t+1/3),a=i(h,s,t),o=i(h,s,t-1/3)}return{r:255*n,g:255*a,b:255*o}}function rgbToHsv(t,r,e){t=bound01(t,255),r=bound01(r,255),e=bound01(e,255);var n,a,o=Math.max(t,r,e),i=Math.min(t,r,e),s=o,h=o-i;if(a=0===o?0:h/o,o==i)n=0;else{switch(o){case t:n=(r-e)/h+(r<e?6:0);break;case r:n=(e-t)/h+2;break;case e:n=(t-r)/h+4}n/=6}return{h:n,s:a,v:s}}function hsvToRgb(t,r,e){t=6*bound01(t,360),r=bound01(r,100),e=bound01(e,100);var n=Math.floor(t),a=t-n,o=e*(1-r),i=e*(1-a*r),s=e*(1-(1-a)*r),h=n%6;return{r:255*[e,i,o,o,s,e][h],g:255*[s,e,e,i,o,o][h],b:255*[o,o,s,e,e,i][h]}}function rgbToHex(t,r,e,n){var a=[pad2(Math.round(t).toString(16)),pad2(Math.round(r).toString(16)),pad2(Math.round(e).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function rgbaToHex(t,r,e,n,a){var o=[pad2(Math.round(t).toString(16)),pad2(Math.round(r).toString(16)),pad2(Math.round(e).toString(16)),pad2(convertDecimalToHex(n))];return a&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1)?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0):o.join("")}function rgbaToArgbHex(t,r,e,n){return[pad2(convertDecimalToHex(n)),pad2(Math.round(t).toString(16)),pad2(Math.round(r).toString(16)),pad2(Math.round(e).toString(16))].join("")}function _desaturate(t,r){r=0===r?0:r||10;var e=tinycolor(t).toHsl();return e.s-=r/100,e.s=clamp01(e.s),tinycolor(e)}function _saturate(t,r){r=0===r?0:r||10;var e=tinycolor(t).toHsl();return e.s+=r/100,e.s=clamp01(e.s),tinycolor(e)}function _greyscale(t){return tinycolor(t).desaturate(100)}function _lighten(t,r){r=0===r?0:r||10;var e=tinycolor(t).toHsl();return e.l+=r/100,e.l=clamp01(e.l),tinycolor(e)}function _brighten(t,r){r=0===r?0:r||10;var e=tinycolor(t).toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-r/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-r/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-r/100*255))),tinycolor(e)}function _darken(t,r){r=0===r?0:r||10;var e=tinycolor(t).toHsl();return e.l-=r/100,e.l=clamp01(e.l),tinycolor(e)}function _spin(t,r){var e=tinycolor(t).toHsl(),n=(e.h+r)%360;return e.h=n<0?360+n:n,tinycolor(e)}function _complement(t){var r=tinycolor(t).toHsl();return r.h=(r.h+180)%360,tinycolor(r)}function polyad(t,r){if(isNaN(r)||r<=0)throw new Error("Argument to polyad must be a positive number");for(var e=tinycolor(t).toHsl(),n=[tinycolor(t)],a=360/r,o=1;o<r;o++)n.push(tinycolor({h:(e.h+o*a)%360,s:e.s,l:e.l}));return n}function _splitcomplement(t){var r=tinycolor(t).toHsl(),e=r.h;return[tinycolor(t),tinycolor({h:(e+72)%360,s:r.s,l:r.l}),tinycolor({h:(e+216)%360,s:r.s,l:r.l})]}function _analogous(t,r,e){r=r||6,e=e||30;var n=tinycolor(t).toHsl(),a=360/e,o=[tinycolor(t)];for(n.h=(n.h-(a*r>>1)+720)%360;--r;)n.h=(n.h+a)%360,o.push(tinycolor(n));return o}function _monochromatic(t,r){r=r||6;for(var e=tinycolor(t).toHsv(),n=e.h,a=e.s,o=e.v,i=[],s=1/r;r--;)i.push(tinycolor({h:n,s:a,v:o})),o=(o+s)%1;return i}tinycolor.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,r,e,n=this.toRgb();return t=n.r/255,r=n.g/255,e=n.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.0722*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))},setAlpha:function(t){return this._a=boundAlpha(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=rgbToHsv(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=rgbToHsv(this._r,this._g,this._b),r=Math.round(360*t.h),e=Math.round(100*t.s),n=Math.round(100*t.v);return 1==this._a?"hsv("+r+", "+e+"%, "+n+"%)":"hsva("+r+", "+e+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var t=rgbToHsl(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=rgbToHsl(this._r,this._g,this._b),r=Math.round(360*t.h),e=Math.round(100*t.s),n=Math.round(100*t.l);return 1==this._a?"hsl("+r+", "+e+"%, "+n+"%)":"hsla("+r+", "+e+"%, "+n+"%, "+this._roundA+")"},toHex:function(t){return rgbToHex(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return rgbaToHex(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*bound01(this._r,255))+"%",g:Math.round(100*bound01(this._g,255))+"%",b:Math.round(100*bound01(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*bound01(this._r,255))+"%, "+Math.round(100*bound01(this._g,255))+"%, "+Math.round(100*bound01(this._b,255))+"%)":"rgba("+Math.round(100*bound01(this._r,255))+"%, "+Math.round(100*bound01(this._g,255))+"%, "+Math.round(100*bound01(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(hexNames[rgbToHex(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var r="#"+rgbaToArgbHex(this._r,this._g,this._b,this._a),e=r,n=this._gradientType?"GradientType = 1, ":"";if(t){var a=tinycolor(t);e="#"+rgbaToArgbHex(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+r+",endColorstr="+e+")"},toString:function(t){var r=!!t;t=t||this._format;var e=!1,n=this._a<1&&this._a>=0;return r||!n||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(e=this.toRgbString()),"prgb"===t&&(e=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(e=this.toHexString()),"hex3"===t&&(e=this.toHexString(!0)),"hex4"===t&&(e=this.toHex8String(!0)),"hex8"===t&&(e=this.toHex8String()),"name"===t&&(e=this.toName()),"hsl"===t&&(e=this.toHslString()),"hsv"===t&&(e=this.toHsvString()),e||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return tinycolor(this.toString())},_applyModification:function(t,r){var e=t.apply(null,[this].concat([].slice.call(r)));return this._r=e._r,this._g=e._g,this._b=e._b,this.setAlpha(e._a),this},lighten:function(){return this._applyModification(_lighten,arguments)},brighten:function(){return this._applyModification(_brighten,arguments)},darken:function(){return this._applyModification(_darken,arguments)},desaturate:function(){return this._applyModification(_desaturate,arguments)},saturate:function(){return this._applyModification(_saturate,arguments)},greyscale:function(){return this._applyModification(_greyscale,arguments)},spin:function(){return this._applyModification(_spin,arguments)},_applyCombination:function(t,r){return t.apply(null,[this].concat([].slice.call(r)))},analogous:function(){return this._applyCombination(_analogous,arguments)},complement:function(){return this._applyCombination(_complement,arguments)},monochromatic:function(){return this._applyCombination(_monochromatic,arguments)},splitcomplement:function(){return this._applyCombination(_splitcomplement,arguments)},triad:function(){return this._applyCombination(polyad,[3])},tetrad:function(){return this._applyCombination(polyad,[4])}},tinycolor.fromRatio=function(t,r){if("object"==_typeof(t)){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[n]="a"===n?t[n]:convertToPercentage(t[n]));t=e}return tinycolor(t,r)},tinycolor.equals=function(t,r){return!(!t||!r)&&tinycolor(t).toRgbString()==tinycolor(r).toRgbString()},tinycolor.random=function(){return tinycolor.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},tinycolor.mix=function(t,r,e){e=0===e?0:e||50;var n=tinycolor(t).toRgb(),a=tinycolor(r).toRgb(),o=e/100;return tinycolor({r:(a.r-n.r)*o+n.r,g:(a.g-n.g)*o+n.g,b:(a.b-n.b)*o+n.b,a:(a.a-n.a)*o+n.a})},tinycolor.readability=function(t,r){var e=tinycolor(t),n=tinycolor(r);return(Math.max(e.getLuminance(),n.getLuminance())+.05)/(Math.min(e.getLuminance(),n.getLuminance())+.05)},tinycolor.isReadable=function(t,r,e){var n,a,o=tinycolor.readability(t,r);switch(a=!1,(n=validateWCAG2Parms(e)).level+n.size){case"AAsmall":case"AAAlarge":a=o>=4.5;break;case"AAlarge":a=o>=3;break;case"AAAsmall":a=o>=7}return a},tinycolor.mostReadable=function(t,r,e){var n,a,o,i,s=null,h=0;a=(e=e||{}).includeFallbackColors,o=e.level,i=e.size;for(var l=0;l<r.length;l++)(n=tinycolor.readability(t,r[l]))>h&&(h=n,s=tinycolor(r[l]));return tinycolor.isReadable(t,s,{level:o,size:i})||!a?s:(e.includeFallbackColors=!1,tinycolor.mostReadable(t,["#fff","#000"],e))};var names=tinycolor.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},hexNames=tinycolor.hexNames=flip(names);function flip(t){var r={};for(var e in t)t.hasOwnProperty(e)&&(r[t[e]]=e);return r}function boundAlpha(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function bound01(t,r){isOnePointZero(t)&&(t="100%");var e=isPercentage(t);return t=Math.min(r,Math.max(0,parseFloat(t))),e&&(t=parseInt(t*r,10)/100),Math.abs(t-r)<1e-6?1:t%r/parseFloat(r)}function clamp01(t){return Math.min(1,Math.max(0,t))}function parseIntFromHex(t){return parseInt(t,16)}function isOnePointZero(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)}function isPercentage(t){return"string"==typeof t&&-1!=t.indexOf("%")}function pad2(t){return 1==t.length?"0"+t:""+t}function convertToPercentage(t){return t<=1&&(t=100*t+"%"),t}function convertDecimalToHex(t){return Math.round(255*parseFloat(t)).toString(16)}function convertHexToDecimal(t){return parseIntFromHex(t)/255}var matchers=function(){var t="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)",r="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?",e="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?";return{CSS_UNIT:new RegExp(t),rgb:new RegExp("rgb"+r),rgba:new RegExp("rgba"+e),hsl:new RegExp("hsl"+r),hsla:new RegExp("hsla"+e),hsv:new RegExp("hsv"+r),hsva:new RegExp("hsva"+e),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function isValidCSSUnit(t){return!!matchers.CSS_UNIT.exec(t)}function stringInputToObject(t){t=t.replace(trimLeft,"").replace(trimRight,"").toLowerCase();var r,e=!1;if(names[t])t=names[t],e=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};return(r=matchers.rgb.exec(t))?{r:r[1],g:r[2],b:r[3]}:(r=matchers.rgba.exec(t))?{r:r[1],g:r[2],b:r[3],a:r[4]}:(r=matchers.hsl.exec(t))?{h:r[1],s:r[2],l:r[3]}:(r=matchers.hsla.exec(t))?{h:r[1],s:r[2],l:r[3],a:r[4]}:(r=matchers.hsv.exec(t))?{h:r[1],s:r[2],v:r[3]}:(r=matchers.hsva.exec(t))?{h:r[1],s:r[2],v:r[3],a:r[4]}:(r=matchers.hex8.exec(t))?{r:parseIntFromHex(r[1]),g:parseIntFromHex(r[2]),b:parseIntFromHex(r[3]),a:convertHexToDecimal(r[4]),format:e?"name":"hex8"}:(r=matchers.hex6.exec(t))?{r:parseIntFromHex(r[1]),g:parseIntFromHex(r[2]),b:parseIntFromHex(r[3]),format:e?"name":"hex"}:(r=matchers.hex4.exec(t))?{r:parseIntFromHex(r[1]+""+r[1]),g:parseIntFromHex(r[2]+""+r[2]),b:parseIntFromHex(r[3]+""+r[3]),a:convertHexToDecimal(r[4]+""+r[4]),format:e?"name":"hex8"}:!!(r=matchers.hex3.exec(t))&&{r:parseIntFromHex(r[1]+""+r[1]),g:parseIntFromHex(r[2]+""+r[2]),b:parseIntFromHex(r[3]+""+r[3]),format:e?"name":"hex"}}function validateWCAG2Parms(t){var r,e;return"AA"!==(r=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase())&&"AAA"!==r&&(r="AA"),"small"!==(e=(t.size||"small").toLowerCase())&&"large"!==e&&(e="small"),{level:r,size:e}}export{tinycolor as default};