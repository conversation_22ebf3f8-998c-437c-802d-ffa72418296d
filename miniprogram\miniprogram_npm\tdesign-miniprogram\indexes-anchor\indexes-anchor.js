import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-indexes-anchor`;let IndexesAnchor=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.properties=props,this.data={prefix:prefix,classPrefix:name,anchorStyle:"",sticky:!1,active:!1},this.relations={"../indexes/indexes":{type:"parent"}}}};IndexesAnchor=__decorate([wxComponent()],IndexesAnchor);export default IndexesAnchor;