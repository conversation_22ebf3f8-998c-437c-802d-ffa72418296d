import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";const{prefix:prefix}=config,name=`${prefix}-swiper-nav`;let SwiperNav=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.properties={current:{type:Number,value:0},total:{type:Number,value:0},type:{type:String,value:"dots"},minShowNum:{type:Number,value:2},showControls:{type:Boolean,value:!1},direction:{type:String,value:"horizontal"},paginationPosition:{type:String,value:"bottom"}},this.relations={"../swiper/swiper":{type:"parent"}},this.data={prefix:prefix,classPrefix:name},this.methods={nav(e){var t;const{dir:r}=e.target.dataset;this.triggerEvent("nav-btn-change",{dir:r,source:"nav"}),this.$parent&&(null===(t=this.$parent)||void 0===t||t.doNavBtnChange(r,"nav"))}}}};SwiperNav=__decorate([wxComponent()],SwiperNav);export default SwiperNav;