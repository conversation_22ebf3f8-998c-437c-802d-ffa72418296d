<wxs src="./slider.wxs" module="t"/><wxs src="../common/utils.wxs" module="_"/><view style="{{_._style([style, customStyle])}}" class="{{_.cls(classPrefix, [['top', label || scaleTextArray.length], ['disabled', disabled], ['range', range]])}} class {{prefix}}-class {{vertical ? classPrefix + '--vertical' : ''}}"><block wx:if="{{!range}}"><text wx:if="{{showExtremeValue}}" class="{{classPrefix}}__value {{classPrefix}}__value--min">{{ label ? t.getValue(label, min) : min}}</text><view id="sliderLine" class="{{_.cls(classPrefix + '__bar', [['disabled', disabled], theme, ['marks', isScale && theme == 'capsule']])}} {{prefix}}-class-bar" bind:tap="onSingleLineTap"><block wx:if="{{isScale}}"><view class="{{_.cls(classPrefix + '__scale-item', [['active', _value >= item.val], ['disabled', disabled], theme, ['hidden', (index == 0 || index == scaleArray.length - 1) && theme == 'capsule' || value == item.val]])}}" wx:for="{{scaleArray}}" wx:key="index" style="{{vertical ? 'top' :  'left'}}:{{item.left}}px; {{vertical ? 'transform: translate(-50%, -50%);' : 'transform: translateX(-50%);'}}" aria-hidden="{{true}}"><view wx:if="{{scaleTextArray.length}}" class="{{_.cls(classPrefix + '__scale-desc', [theme])}}">{{scaleTextArray[index]}}</view></view></block><view class="{{_.cls(classPrefix + '__line', [['disabled', disabled], theme, 'single'])}} {{prefix}}-class-bar-active" style="{{vertical ? 'height' : 'width'}}: {{lineBarWidth}}"><view id="singleDot" class="{{classPrefix}}__dot {{prefix}}-class-cursor" catch:touchstart="onTouchStart" catch:touchmove="onSingleLineTap" catch:touchend="onTouchEnd" catch:touchcancel="onTouchEnd"><view wx:if="{{label || isVisibleToScreenReader}}" class="{{_.cls(classPrefix+'__dot-value', [['sr-only', !label]])}}" aria-role="alert" aria-live="assertive" aria-hidden="{{!isVisibleToScreenReader}}">{{t.getValue(label, _value) || _value}}</view><view class="{{classPrefix}}__dot-slider" aria-role="slider" aria-disabled="{{disabled}}" aria-valuemax="{{max}}" aria-valuemin="{{min}}" aria-valuenow="{{_value}}" aria-valuetext="{{t.getValue(label, _value) || _value}}"></view></view></view></view><text wx:if="{{showExtremeValue}}" class="{{classPrefix}}__value {{classPrefix}}__value--max">{{ label ? t.getValue(label, max) : max}}</text></block><block wx:if="{{range}}"><view wx:if="{{showExtremeValue}}" class="{{classPrefix}}__range-extreme {{classPrefix}}__range-extreme--min">{{min}}</view><view id="sliderLine" bind:tap="onLineTap" class="{{_.cls(classPrefix + '__bar', [['disabled', disabled], theme, ['marks', isScale && theme == 'capsule']])}} {{prefix}}-class-bar"><block wx:if="{{isScale}}"><view class="{{_.cls(classPrefix + '__scale-item', [['active', dotTopValue[1] >= item.val && item.val >= dotTopValue[0]], ['disabled', disabled], theme, ['hidden', (index == 0 || index == scaleArray.length - 1) && theme == 'capsule' || value == item.val]])}}" wx:for="{{scaleArray}}" wx:for-index="index" wx:key="index" style="{{vertical ? 'top' :  'left'}}:{{item.left}}px; {{vertical ? 'transform: translate(-50%, -50%);' : 'transform: translateX(-50%);'}}" aria-hidden="{{true}}"><view wx:if="{{scaleTextArray.length}}" class="{{_.cls(classPrefix + '__scale-desc', [theme])}}">{{scaleTextArray[index]}}</view></view></block><view class="{{_.cls(classPrefix + '__line', [['disabled', disabled], theme])}} {{prefix}}-class-bar-active" style="{{vertical ? 'top' : 'left'}}: {{lineLeft}}px; {{vertical ? 'bottom' : 'right'}}: {{lineRight}}px"><view id="leftDot" catch:touchstart="onTouchStart" catch:touchmove="onTouchMoveLeft" catch:touchend="onTouchEnd" catch:touchcancel="onTouchEnd" class="{{classPrefix}}__dot {{classPrefix}}__dot--left {{prefix}}-class-cursor"><view wx:if="{{label || isVisibleToScreenReader}}" class="{{_.cls(classPrefix+'__dot-value', [['sr-only', !label]])}}" aria-role="alert" aria-live="assertive" aria-hidden="{{!isVisibleToScreenReader}}">{{t.getValue(label, dotTopValue[0]) || dotTopValue[0]}}</view><view class="{{classPrefix}}__dot-slider" aria-role="slider" aria-disabled="{{disabled}}" aria-valuemax="{{max}}" aria-valuemin="{{min}}" aria-valuenow="{{dotTopValue[0]}}" aria-valuetext="{{t.getValue(label, dotTopValue[0]) || dotTopValue[0]}}"></view></view><view id="rightDot" catch:touchstart="onTouchStart" catch:touchmove="onTouchMoveRight" catch:touchend="onTouchEnd" catch:touchcancel="onTouchEnd" class="{{classPrefix}}__dot {{classPrefix}}__dot--right {{prefix}}-class-cursor"><view wx:if="{{label || isVisibleToScreenReader}}" class="{{_.cls(classPrefix+'__dot-value', [['sr-only', !label]])}}" aria-role="alert" aria-live="assertive" aria-hidden="{{!isVisibleToScreenReader}}">{{t.getValue(label, dotTopValue[1]) || dotTopValue[1]}}</view><view class="{{classPrefix}}__dot-slider" aria-role="slider" aria-disabled="{{disabled}}" aria-valuemax="{{max}}" aria-valuemin="{{min}}" aria-valuenow="{{dotTopValue[1]}}" aria-valuetext="{{t.getValue(label, dotTopValue[1]) || dotTopValue[1]}}"></view></view></view></view><view wx:if="{{showExtremeValue}}" class="{{classPrefix}}__range-extreme {{classPrefix}}__range-extreme--max">{{max}}</view></block></view>