import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getBackgroundColor}from"./utils";import{unitConvert,isIOS as isIOSValidator}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-progress`;let Progress=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-bar`,`${prefix}-class-label`],this.options={multipleSlots:!0},this.properties=props,this.data={prefix:prefix,classPrefix:name,colorBar:"",heightBar:"",computedStatus:"",computedProgress:0,isIOS:!1},this.observers={percentage(o){o=Math.max(0,Math.min(o,100)),this.setData({computedStatus:100===o?"success":"",computedProgress:o})},color(o){this.setData({colorBar:getBackgroundColor(o),colorCircle:"object"==typeof o?"":o})},strokeWidth(o){if(!o)return"";this.setData({heightBar:unitConvert(o)})},trackColor(o){this.setData({bgColorBar:o})}}}attached(){const o=isIOSValidator();this.setData({isIOS:o})}};Progress=__decorate([wxComponent()],Progress);export default Progress;