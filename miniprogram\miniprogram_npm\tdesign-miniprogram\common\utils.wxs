/* utils */

/**
 * addUnit */
// 为 css 添加单位
function addUnit(value) {
  // prettier-ignore
  var REGEXP = getRegExp('^-?\d+(.\d+)?$');
  if (value == null) {
    return undefined;
  }
  return REGEXP.test('' + value) ? value + 'px' : value;
}

function isString(string) {
  return string && string.constructor === 'String';
}

function isArray(array) {
  return array && array.constructor === 'Array';
}

function isObject(obj) {
  return obj && obj.constructor === 'Object';
}

function isBoolean(value) {
  return typeof value === 'boolean';
}

var isNoEmptyObj = function (obj) {
  return isObject(obj) && JSON.stringify(obj) !== '{}';
};

function includes(arr, value) {
  if (!arr || !isArray(arr)) return false;

  var i = 0;
  var len = arr.length;

  for (; i < len; i++) {
    if (arr[i] === value) return true;
  }
  return false;
}

function cls(base, arr) {
  var res = [base];
  var i = 0;
  for (var size = arr.length; i < size; i++) {
    var item = arr[i];

    if (item && item.constructor === 'Array') {
      var key = arr[i][0];
      var value = arr[i][1];

      if (value) {
        res.push(base + '--' + key);
      }
    } else if (typeof item === 'string' || typeof item === 'number') {
      if (item) {
        res.push(base + '--' + item);
      }
    }
  }
  return res.join(' ');
}

function getBadgeAriaLabel(options) {
  var maxCount = options.maxCount || 99;
  if (options.dot) {
    return '有新的消息';
  }
  if (options.count === '...') {
    return '有很多消息';
  }
  if (isNaN(options.count)) {
    return options.count;
  }
  var str1 = '有' + maxCount + '+条消息';
  var str2 = '有' + options.count + '条消息';
  return Number(options.count) > maxCount ? str1 : str2;
}

function endsWith(str, endStr) {
  return str.slice(-endStr.length) === endStr ? str : str + endStr;
}

function keys(obj) {
  return JSON.stringify(obj)
    .replace(getRegExp('{|}|"', 'g'), '')
    .split(',')
    .map(function (item) {
      return item.split(':')[0];
    });
}

function kebabCase(str) {
  return str
    .replace(getRegExp('[A-Z]', 'g'), function (ele) {
      return '-' + ele;
    })
    .toLowerCase();
}

function _style(styles) {
  if (isArray(styles)) {
    return styles
      .filter(function (item) {
        return item != null && item !== '';
      })
      .map(function (item) {
        return isArray(item) ? _style(item) : endsWith(item, ';');
      })
      .join(' ');
  }

  if (isObject(styles)) {
    return keys(styles)
      .filter(function (key) {
        return styles[key] != null && styles[key] !== '';
      })
      .map(function (key) {
        return [kebabCase(key), [styles[key]]].join(':');
      })
      .join(';');
  }

  return styles;
}

function isValidIconName(str) {
  // prettier-ignore
  return getRegExp('^[A-Za-z0-9\-]+$').test(str);
}

module.exports = {
  addUnit: addUnit,
  isString: isString,
  isArray: isArray,
  isObject: isObject,
  isBoolean: isBoolean,
  isNoEmptyObj: isNoEmptyObj,
  includes: includes,
  cls: cls,
  getBadgeAriaLabel: getBadgeAriaLabel,
  _style: _style,
  isValidIconName: isValidIconName,
};
