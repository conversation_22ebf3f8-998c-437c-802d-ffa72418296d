// Coze API 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')
const jwt = require('jsonwebtoken')

cloud.init({
  env: 'cloud1-2g263bi11fd6ea28'
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const {
    COZE_API_KEY,
    COZE_API_BASE_URL,
    COZE_CLIENT_ID,
    COZE_CLIENT_SECRET
  } = process.env

  // 从环境变量或数据中获取API配置
  let apiKey = COZE_API_KEY || data?.apiKey
  const apiBaseUrl = COZE_API_BASE_URL || data?.apiBaseUrl || 'https://api.coze.cn/open_api/v2'

  // 获取OAuth配置（优先使用环境变量，然后使用云数据库中的配置）
  let clientId = COZE_CLIENT_ID
  let clientSecret = COZE_CLIENT_SECRET

  // 如果环境变量中没有OAuth配置，尝试从云数据库获取
  if (!clientId || !clientSecret) {
    try {
      const oauthConfig = await getOAuthConfigFromDB()
      if (oauthConfig) {
        clientId = clientId || oauthConfig.clientId
        clientSecret = clientSecret || oauthConfig.clientSecret
      }
    } catch (error) {
      console.log('从数据库获取OAuth配置失败:', error.message)
    }
  }

  // 如果没有直接的API密钥，尝试使用OAuth
  if (!apiKey && clientId && clientSecret) {
    console.log('使用OAuth方式获取访问令牌')
    try {
      apiKey = await getOAuthAccessToken(clientId, clientSecret)
    } catch (error) {
      console.error('OAuth获取访问令牌失败:', error)
      return {
        success: false,
        error: 'OAuth authentication failed: ' + error.message
      }
    }
  }

  // 如果仍然没有API密钥，返回错误
  if (!apiKey) {
    console.error('No valid API key or OAuth credentials available')
    return {
      success: false,
      error: 'No valid API key or OAuth credentials configured. Please set COZE_API_KEY or OAuth credentials in environment variables.'
    }
  }

  console.log('使用的Coze API地址:', apiBaseUrl)
  console.log('API Key前4位:', apiKey.substring(0, 4) + '...')
  
  // 配置axios请求头
  const axiosConfig = {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    // 将超时设置为45秒
    timeout: 45000
  }

  try {
    // 根据不同的操作执行不同的功能
    switch (action) {
      case 'testConnection':
        return await testCozeConnection(apiBaseUrl, axiosConfig)
      
      case 'createConversation':
        return await createCozeConversation(data, apiBaseUrl, axiosConfig)
      
      case 'sendMessage':
        return await sendCozeMessage(data, apiBaseUrl, axiosConfig)
      
      case 'pollMessage':
        return await pollCozeMessage(data, apiBaseUrl, axiosConfig)
      
      case 'getConversationHistory':
        return await getCozeConversationHistory(data, apiBaseUrl, axiosConfig)

      case 'saveOAuthConfig':
        return await saveOAuthConfig(data)

      case 'listBots':
        return await listCozeBots(apiBaseUrl, axiosConfig)

      default:
        return {
          success: false,
          error: `Unknown action: ${action}`
        }
    }
  } catch (error) {
    console.error('Coze API调用失败:', error)
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    }
  }
}

// 测试Coze API连接
async function testCozeConnection(apiBaseUrl, axiosConfig) {
  try {
    console.log('=== 测试Coze API连接 ===')
    console.log('测试URL:', `${apiBaseUrl}/chat`)
    
    // 发送一个简单的测试请求
    const testResponse = await axios.post(
      `${apiBaseUrl}/chat`,
      {
        conversation_id: "test_conversation",
        bot_id: "test_bot",
        user: "test_user",
        query: "Hello",
        stream: false
      },
      axiosConfig
    )

    console.log('测试请求成功，状态码:', testResponse.status)
    return {
      success: true,
      message: 'Coze API连接成功',
      status: testResponse.status
    }
  } catch (error) {
    console.error('测试Coze API连接失败:', error.response?.data || error.message)
    return {
      success: false,
      error: `连接失败: ${error.response?.data?.msg || error.message}`,
      status: error.response?.status
    }
  }
}

// 创建Coze对话
async function createCozeConversation(data, apiBaseUrl, axiosConfig) {
  try {
    console.log('=== 创建Coze对话 ===')
    const { agentId } = data
    
    // Coze使用bot_id而不是agent_id
    const botId = agentId || 'default_bot'
    
    // 生成一个新的对话ID
    const conversationId = 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    
    console.log('创建对话ID:', conversationId)
    console.log('使用Bot ID:', botId)
    
    return {
      success: true,
      conversation_id: conversationId,
      bot_id: botId,
      welcome_message: '你好！我是Coze AI助手，有什么可以帮助你的吗？'
    }
  } catch (error) {
    console.error('创建Coze对话失败:', error)
    return {
      success: false,
      error: error.message || '创建对话失败'
    }
  }
}

// 发送消息到Coze
async function sendCozeMessage(data, apiBaseUrl, axiosConfig) {
  try {
    console.log('=== 发送消息到Coze ===')
    const { conversationId, message, agentId, user, streaming } = data

    const botId = agentId || 'default_bot'
    const userId = user || 'default_user'

    console.log('对话ID:', conversationId)
    console.log('Bot ID:', botId)
    console.log('用户ID:', userId)
    console.log('消息内容:', message)
    console.log('流式模式:', streaming)

    const requestBody = {
      conversation_id: conversationId,
      bot_id: botId,
      user: userId,
      query: message,
      stream: false // Coze暂时使用非流式模式
    }

    console.log('发送消息请求体:', JSON.stringify(requestBody, null, 2))
    console.log('请求URL:', `${apiBaseUrl}/chat`)

    try {
      console.log('发送请求到Coze API:', `${apiBaseUrl}/chat`)

      // 发送请求到Coze API
      const response = await axios.post(
        `${apiBaseUrl}/chat`,
        requestBody,
        axiosConfig
      )

      console.log('Coze API响应状态:', response.status)
      console.log('Coze API响应数据:', JSON.stringify(response.data, null, 2))

      const responseData = response.data

      // 处理Coze API的响应格式
      if (responseData.code === 0) {
        // 成功响应
        console.log('Coze API调用成功')

        // 从messages数组中提取assistant的回复
        const messages = responseData.messages || []
        const assistantMessage = messages.find(msg => msg.role === 'assistant' && msg.type === 'answer')

        if (assistantMessage) {
          return {
            success: true,
            message_id: `coze_${Date.now()}`,
            conversation_id: responseData.conversation_id || conversationId,
            answer: assistantMessage.content || '收到回复',
            created_at: Math.floor(Date.now() / 1000),
            status: 'completed'
          }
        } else {
          console.warn('未找到assistant回复消息')
          return {
            success: true,
            message_id: `coze_${Date.now()}`,
            conversation_id: responseData.conversation_id || conversationId,
            answer: '收到回复，但内容为空',
            created_at: Math.floor(Date.now() / 1000),
            status: 'completed'
          }
        }
      } else {
        // 错误响应
        console.error('Coze API返回错误:', responseData)
        return {
          success: false,
          error: responseData.msg || 'Coze API调用失败',
          error_code: responseData.code
        }
      }

    } catch (requestError) {
      console.error('请求Coze API失败:', requestError.response?.data || requestError.message)
      
      return {
        success: false,
        error: `请求失败: ${requestError.response?.data?.msg || requestError.message}`,
        error_code: requestError.response?.status || 500
      }
    }

  } catch (error) {
    console.error('发送消息到Coze失败:', error)
    return {
      success: false,
      error: error.message || '发送消息失败'
    }
  }
}

// 轮询Coze消息状态
async function pollCozeMessage(data, apiBaseUrl, axiosConfig) {
  try {
    console.log('=== 轮询Coze消息状态 ===')
    const { messageId } = data
    
    console.log('轮询消息ID:', messageId)
    
    // Coze的消息轮询接口
    const response = await axios.get(
      `${apiBaseUrl}/chat/message/list?conversation_id=${messageId}`,
      axiosConfig
    )
    
    console.log('轮询响应:', response.data)
    
    return {
      success: true,
      data: response.data
    }
    
  } catch (error) {
    console.error('轮询Coze消息失败:', error)
    return {
      success: false,
      error: error.message || '轮询消息失败'
    }
  }
}

// 获取Coze对话历史
async function getCozeConversationHistory(data, apiBaseUrl, axiosConfig) {
  try {
    console.log('=== 获取Coze对话历史 ===')
    const { conversationId, firstMessageId, limit } = data
    
    console.log('对话ID:', conversationId)
    console.log('起始消息ID:', firstMessageId)
    console.log('限制数量:', limit)
    
    // 构建查询参数
    let url = `${apiBaseUrl}/chat/message/list?conversation_id=${conversationId}`
    if (limit) {
      url += `&limit=${limit}`
    }
    if (firstMessageId) {
      url += `&first_id=${firstMessageId}`
    }

    const response = await axios.get(url, axiosConfig)
    
    console.log('历史记录响应:', response.data)
    
    return {
      success: true,
      data: response.data.data || [],
      has_more: response.data.has_more || false
    }
    
  } catch (error) {
    console.error('获取Coze对话历史失败:', error)
    return {
      success: false,
      error: error.message || '获取对话历史失败',
      data: [],
      has_more: false
    }
  }
}

// OAuth相关函数

// 获取OAuth访问令牌
async function getOAuthAccessToken(clientId, clientSecret) {
  try {
    console.log('=== 获取OAuth访问令牌 ===')

    // 临时跳过缓存检查，直接获取新令牌
    console.log('跳过缓存检查，直接获取新令牌')

    console.log('获取新的访问令牌')

    // 检查是否是JWT类型的应用（私钥以-----BEGIN开头）
    if (clientSecret.includes('-----BEGIN')) {
      console.log('检测到JWT类型应用，生成JWT token')

      // 处理私钥格式
      let privateKey = clientSecret.trim()

      // 移除所有现有的换行符和空格，重新格式化
      privateKey = privateKey
        .replace(/\\n/g, '\n') // 将字面量\n转换为真正的换行符
        .replace(/\s+/g, ' ') // 将多个空格合并为一个
        .replace(/-----BEGIN PRIVATE KEY----- /g, '-----BEGIN PRIVATE KEY-----\n')
        .replace(/ -----END PRIVATE KEY-----/g, '\n-----END PRIVATE KEY-----')
        .trim()

      // 如果私钥是一行的，需要重新格式化
      if (!privateKey.includes('\n') || privateKey.split('\n').length < 3) {
        // 提取私钥内容（去掉BEGIN和END行）
        const keyContent = privateKey
          .replace('-----BEGIN PRIVATE KEY-----', '')
          .replace('-----END PRIVATE KEY-----', '')
          .replace(/\s/g, '') // 移除所有空格和换行

        // 重新构建私钥，每64个字符一行
        const lines = []
        for (let i = 0; i < keyContent.length; i += 64) {
          lines.push(keyContent.substr(i, 64))
        }

        privateKey = '-----BEGIN PRIVATE KEY-----\n' + lines.join('\n') + '\n-----END PRIVATE KEY-----'
      }

      console.log('私钥格式处理完成，长度:', privateKey.length)
      console.log('私钥开头:', privateKey.substring(0, 50) + '...')

      // 生成JWT token
      const payload = {
        iss: clientId,
        aud: 'api.coze.cn',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
        sub: clientId
      }

      try {
        const token = jwt.sign(payload, privateKey, { algorithm: 'RS256' })
        console.log('JWT token生成成功')
        return `Bearer ${token}`
      } catch (error) {
        console.error('JWT token生成失败:', error)
        console.error('私钥内容（前100字符）:', privateKey.substring(0, 100))
        throw new Error('JWT token生成失败: ' + error.message)
      }
    }

    // 使用客户端凭证模式获取访问令牌
    const response = await axios.post('https://api.coze.cn/api/permission/oauth2/token', {
      grant_type: 'client_credentials',
      client_id: clientId,
      client_secret: clientSecret
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    })

    console.log('OAuth响应状态:', response.status)
    console.log('OAuth响应数据:', response.data)

    if (response.data && response.data.access_token) {
      const tokenData = {
        access_token: response.data.access_token,
        expires_in: response.data.expires_in || 3600,
        created_at: Date.now()
      }

      // 临时跳过缓存
      console.log('跳过令牌缓存')

      return tokenData.access_token
    } else {
      throw new Error('Invalid OAuth response: ' + JSON.stringify(response.data))
    }

  } catch (error) {
    console.error('获取OAuth访问令牌失败:', error.response?.data || error.message)
    throw new Error('Failed to get OAuth access token: ' + (error.response?.data?.error_description || error.message))
  }
}

// 缓存访问令牌到云数据库
async function cacheAccessToken(tokenData) {
  try {
    const db = cloud.database()
    const collection = db.collection('coze_tokens')

    // 删除旧的令牌
    await collection.where({}).remove()

    // 保存新令牌
    await collection.add({
      data: {
        ...tokenData,
        _id: 'current_token'
      }
    })

    console.log('访问令牌已缓存')
  } catch (error) {
    console.error('缓存访问令牌失败:', error)
    // 缓存失败不影响主流程
  }
}

// 从云数据库获取缓存的访问令牌
async function getCachedAccessToken() {
  try {
    const db = cloud.database()
    const collection = db.collection('coze_tokens')

    const result = await collection.limit(1).get()

    if (result.data && result.data.length > 0) {
      return result.data[0]
    }

    return null
  } catch (error) {
    console.error('获取缓存访问令牌失败:', error)
    return null
  }
}

// 检查令牌是否过期
function isTokenExpired(tokenData) {
  if (!tokenData || !tokenData.created_at || !tokenData.expires_in) {
    return true
  }

  const expirationTime = tokenData.created_at + (tokenData.expires_in * 1000)
  const currentTime = Date.now()

  // 提前5分钟刷新令牌
  const bufferTime = 5 * 60 * 1000

  return currentTime >= (expirationTime - bufferTime)
}

// 从云数据库获取OAuth配置
async function getOAuthConfigFromDB() {
  try {
    const db = cloud.database()
    const collection = db.collection('coze_oauth_config')

    const result = await collection.limit(1).get()

    if (result.data && result.data.length > 0) {
      return result.data[0]
    }

    return null
  } catch (error) {
    console.error('获取OAuth配置失败:', error)
    return null
  }
}

// 保存OAuth配置到云数据库
async function saveOAuthConfigToDB(clientId, clientSecret, apiBaseUrl) {
  try {
    const db = cloud.database()
    const collection = db.collection('coze_oauth_config')

    // 删除旧配置
    await collection.where({}).remove()

    // 保存新配置
    await collection.add({
      data: {
        clientId,
        clientSecret,
        apiBaseUrl,
        updatedAt: new Date()
      }
    })

    console.log('OAuth配置已保存到数据库')
    return true
  } catch (error) {
    console.error('保存OAuth配置失败:', error)
    return false
  }
}

// 保存OAuth配置
async function saveOAuthConfig(data) {
  try {
    const { clientId, clientSecret, apiBaseUrl } = data

    if (!clientId || !clientSecret) {
      return {
        success: false,
        error: 'Client ID and Client Secret are required'
      }
    }

    const success = await saveOAuthConfigToDB(clientId, clientSecret, apiBaseUrl)

    if (success) {
      return {
        success: true,
        message: 'OAuth configuration saved successfully'
      }
    } else {
      return {
        success: false,
        error: 'Failed to save OAuth configuration'
      }
    }
  } catch (error) {
    console.error('保存OAuth配置失败:', error)
    return {
      success: false,
      error: error.message || 'Failed to save OAuth configuration'
    }
  }
}

// 获取Coze智能体列表
async function listCozeBots(apiBaseUrl, axiosConfig) {
  console.log('=== 获取Coze智能体列表 ===')

  // 尝试不同的可能端点
  const possibleEndpoints = [
    '/bot/list',
    '/bots/list',
    '/workspace/bots',
    '/v1/bot/list',
    '/bot'
  ]

  for (const endpoint of possibleEndpoints) {
    const listUrl = `${apiBaseUrl}${endpoint}`
    console.log('尝试请求URL:', listUrl)

    try {
      const response = await axios.get(listUrl, axiosConfig)
      console.log('获取智能体列表成功，状态码:', response.status)
      console.log('响应数据:', JSON.stringify(response.data, null, 2))

      return {
        success: true,
        data: response.data?.data || response.data || [],
        message: `获取智能体列表成功 (端点: ${endpoint})`,
        endpoint: endpoint
      }
    } catch (error) {
      console.log(`端点 ${endpoint} 失败:`, error.response?.status || error.message)
      continue
    }
  }

  // 如果所有端点都失败，返回错误
  return {
    success: false,
    error: '所有智能体列表端点都返回404，可能需要查看Coze API文档确认正确的端点',
    attempted_endpoints: possibleEndpoints
  }
}
