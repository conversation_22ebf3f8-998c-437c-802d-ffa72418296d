// 云函数入口文件
const cloud = require('wx-server-sdk')
const got = require('got')

cloud.init({
  env: 'cloud1-2g263bi11fd6ea28'
})

// 云函数入口函数
exports.main = async (event, context) => {
  const { url, method = 'GET', body, headers = {} } = event
  
  try {
    const response = await got(url, {
      method,
      body,
      headers,
      responseType: 'text'
    })
    
    return {
      code: 0,
      message: '请求成功',
      status: response.statusCode,
      headers: response.headers,
      body: response.body
    }
  } catch (error) {
    console.error('HTTP请求失败', error)
    return {
      code: -1,
      message: '请求失败: ' + error.message,
      error: {
        message: error.message,
        stack: error.stack
      }
    }
  }
} 