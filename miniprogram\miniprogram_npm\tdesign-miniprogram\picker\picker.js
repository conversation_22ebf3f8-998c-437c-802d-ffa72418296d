import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import{rpx2px}from"../common/utils";import config from"../common/config";import props from"./props";import useCustomNavbar from"../mixins/using-custom-navbar";const{prefix:prefix}=config,name=`${prefix}-picker`;let Picker=class extends SuperComponent{constructor(){super(...arguments),this.behaviors=[useCustomNavbar],this.properties=props,this.externalClasses=[`${prefix}-class`,`${prefix}-class-confirm`,`${prefix}-class-cancel`,`${prefix}-class-title`],this.options={multipleSlots:!0},this.relations={"../picker-item/picker-item":{type:"child",linked(){this.updateChildren()}}},this.observers={"value, visible"(){this.updateChildren()}},this.lifetimes={attached(){this.setData({pickItemHeight:rpx2px(this.properties.itemHeight)})}},this.data={prefix:prefix,classPrefix:name,defaultPopUpProps:{},defaultPopUpzIndex:11500,pickItemHeight:0},this.methods={updateChildren(){const{pickItemHeight:e}=this.data,{value:t,defaultValue:i}=this.properties;this.$children.forEach(((s,r)=>{var l,n;s.setData({value:null!==(n=null!==(l=null==t?void 0:t[r])&&void 0!==l?l:null==i?void 0:i[r])&&void 0!==n?n:"",columnIndex:r,pickItemHeight:e}),s.update()}))},getSelectedValue(){return[this.$children.map((e=>e._selectedValue)),this.$children.map((e=>e._selectedLabel))]},getColumnIndexes(){return this.$children.map(((e,t)=>({column:t,index:e._selectedIndex})))},onConfirm(){const[e,t]=this.getSelectedValue(),i=this.getColumnIndexes();this.close("confirm-btn"),this.triggerEvent("confirm",{value:e,label:t,columns:i}),JSON.stringify(this.data.value)!==JSON.stringify(e)&&this.triggerEvent("change",{value:e,label:t,columns:i})},triggerColumnChange({column:e,index:t}){const[i,s]=this.getSelectedValue();this.triggerEvent("pick",{value:i,label:s,column:e,index:t})},onCancel(){this.close("cancel-btn"),this.triggerEvent("cancel")},onPopupChange(e){const{visible:t}=e.detail;this.close("overlay"),this.triggerEvent("visible-change",{visible:t})},close(e){this.data.autoClose&&this.setData({visible:!1}),this.triggerEvent("close",{trigger:e})}}}ready(){this.$children.map(((e,t)=>e.columnIndex=t))}};Picker=__decorate([wxComponent()],Picker);export default Picker;