<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><navigator class="{{className}} class {{prefix}}-class" style="{{_._style([style, customStyle])}}" target="{{navigatorProps.target}}" url="{{ !disabled && navigatorProps.url}}" open-type="{{navigatorProps.openType || 'navigate'}}" delta="{{navigatorProps.delta}}" app-id="{{navigatorProps.appId}}" path="{{navigatorProps.path}}" extra-data="{{navigatorProps.extraData}}" version="{{navigatorProps.version}}" short-link="{{navigatorProps.shortLink}}" hover-class="{{ hover && !disabled && classPrefix + '--hover' }} {{prefix}}-class-hover {{navigatorProps.hoverClass}}" hover-stop-propagation="navigatorProps.hoverStopPropagation" hover-start-time="{{navigatorProps.hoverStartTime}}" hover-stay-time="{{navigatorProps.hoverStayTime}}" bindsuccess="onSuccess" bindfail="onFail" bindcomplete="onComplete" aria-disabled="{{disabled}}"><view class="{{classPrefix}}__prefix-icon {{prefix}}-class-prefix-icon"><slot name="prefix-icon"/><template wx:if="{{_prefixIcon}}" is="icon" data="{{tClass: prefix + '-class-prefix-icon', ariaHidden: true, ..._prefixIcon }}"/></view><view class="{{classPrefix}}__content {{prefix}}-class-content"><block wx:if="{{content}}">{{content}}</block><slot name="content"/><slot/></view><view class="{{classPrefix}}__suffix-icon {{prefix}}-class-suffix-icon"><slot name="suffix-icon"/><template wx:if="{{_suffixIcon}}" is="icon" data="{{tClass: prefix + '-class-suffix-icon', ariaHidden: true, ..._suffixIcon }}"/></view></navigator>