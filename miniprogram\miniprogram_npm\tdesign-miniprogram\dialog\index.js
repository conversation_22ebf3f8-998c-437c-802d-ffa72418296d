import{__rest}from"tslib";import props from"./props";import{getInstance}from"../common/utils";const defaultOptions={actions:[],buttonLayout:props.buttonLayout.value,cancelBtn:props.cancelBtn.value,closeOnOverlayClick:props.closeOnOverlayClick.value,confirmBtn:props.confirmBtn.value,content:"",preventScrollThrough:props.preventScrollThrough.value,showOverlay:props.showOverlay.value,title:"",visible:props.visible.value};export default{alert(t){const e=Object.assign({},t),{context:s,selector:o="#t-dialog"}=e,n=__rest(e,["context","selector"]),c=getInstance(s,o);return c?new Promise((t=>{const e=Object.assign(Object.assign(Object.assign({},defaultOptions),c.properties),n);c.setData(Object.assign(Object.assign({cancelBtn:""},e),{visible:!0})),c._onConfirm=t})):Promise.reject()},confirm(t){const e=Object.assign({},t),{context:s,selector:o="#t-dialog"}=e,n=__rest(e,["context","selector"]),c=getInstance(s,o);return c?new Promise(((t,e)=>{const s=Object.assign(Object.assign(Object.assign({},defaultOptions),c.properties),n);c.setData(Object.assign(Object.assign({},s),{visible:!0})),c._onConfirm=t,c._onCancel=e})):Promise.reject()},close(t){const{context:e,selector:s="#t-dialog"}=Object.assign({},t),o=getInstance(e,s);return o?(o.close(),Promise.resolve()):Promise.reject()},action(t){const e=Object.assign({},t),{context:s,selector:o="#t-dialog"}=e,n=__rest(e,["context","selector"]),c=getInstance(s,o);if(!c)return Promise.reject();const{buttonLayout:r="vertical",actions:i=c.properties.actions}=t,a="vertical"===r?7:3;return(!i||"object"==typeof i&&(0===i.length||i.length>a))&&console.warn(`action 数量建议控制在1至${a}个`),new Promise((t=>{const e=Object.assign(Object.assign(Object.assign({},defaultOptions),c.properties),n);c.setData(Object.assign(Object.assign({},e),{buttonLayout:r,visible:!0})),c._onAction=t}))}};